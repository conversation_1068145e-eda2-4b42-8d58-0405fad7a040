export class HttpError extends Error {
  status: number;

  constructor(message: string, status: number) {
    super(message);
    this.status = status;
    this.name = "HttpError";
    // Ensures proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, HttpError.prototype);
  }
}

export interface APISuccessResponse<T> {
  success: true;
  data: T;
}
export interface APIPaginatedSuccessResponse<T> {
  success: true;
  data: T;
  total: number;
}
export interface APIErrorResponse {
  success: false;
  data: {
    code: string;
    message: string;
  };
}
export type APIResponse<T> = APISuccessResponse<T> | APIErrorResponse;
export type APIPaginatedResponse<T> =
  | APIPaginatedSuccessResponse<T>
  | APIErrorResponse;

export interface JoinedCohort {
  bio: string | null;
  createdAt: string;
  endDate: string | null;
  groupId: number;
  id: number;
  externalId: string;
  isDefault: boolean;
  name: string;
  startDate: string;
  updatedAt: string;
}

export interface MyGroup {
  bannerImage: string;
  bio: string | null;
  createdAt: string;
  creatorId: number;
  description: string;
  id: number;
  isActive: boolean;
  joinedCohorts: JoinedCohort[];
  name: string;
  organizationId: number;
  tags: string[];
  updatedAt: string;
  externalId: string;
}

export interface MyGroupsResponse {
  success: boolean;
  data: {
    groups: MyGroup[];
  };
}

export interface Group {
  bannerImage: string;
  createdAt: string;
  creatorName: string;
  description: string;
  id: number;
  externalId: string;
  name: string;
  organizationName: string;
  tags?: string[];
}

export interface GroupsResponse {
  success: boolean;
  total: number;
  data: {
    groups: Group[];
  };
}

interface BaseModule {
  id: number;
  name: string;
  order: number;
  createdAt: string;
}
export interface CourseModule extends BaseModule {
  config: { courseId: number };
  type: "course";
}
export interface FeedModule extends BaseModule {
  config: {
    feedId: string;
    feedName: string;
    feedGroup: string;
    userId: string;
  };
  type: "feed";
}
export interface EventsModule extends BaseModule {
  config: {};
  type: "events";
}
export interface DiscussionModule extends BaseModule {
  config: {
    channelId: string;
    channelName: string;
    channelType: string;
  };
  type: "discussion";
}
export interface LinksModule extends BaseModule {
  config: {
    attachments: {
      attachmentType: "link" | "document";
      title: string;
      url: string;
    }[];
  };
  type: "links";
}
export type Module =
  | CourseModule
  | FeedModule
  | EventsModule
  | DiscussionModule
  | LinksModule;

export interface ModuleResponse {
  success: boolean;
  data: Module;
}

export interface GroupCohort {
  id: number;
  externalId: string;
  name: string;
  bio: string;
  isDefault: boolean;
  createdAt: string;
  startDate: string;
  endDate: string;
  groupId: number;
}

export interface Cohort extends GroupCohort {
  modules: Module[];
}

export interface CohortResponse {
  success: boolean;
  data: Cohort;
}

export interface GroupCohortsResponse {
  success: boolean;
  total: number;
  data: {
    cohorts: GroupCohort[];
  };
}

export interface Group {
  bannerImage: string;
  createdAt: string;
  creatorName: string;
  creatorBio: string;
  creatorAvatarURL: string;
  creatorSupertokensUserId: string;
  description: string;
  bio: string;
  id: number;
  modules: Module[];
  defaultCohortModules: Module[];
  name: string;
  organizationName: string;
  totalLessons: number;
  totalMembers: number;
}

export type GroupResponse = {
  success: boolean;
  data: Group;
};

export interface LessonAttachment {
  attachmentType: "document" | "link";
  id: number;
  title: string;
  url: string;
}

export interface VideoLessonContent {
  contentType: "video";
  metadata: {
    url: string;
  };
}

export interface AudioLessonContent {
  contentType: "audio";
  metadata: {
    url: string;
  };
}

export interface ArticleLessonContent {
  contentType: "content";
  metadata: {
    content: string;
  };
}

export type LessonContent =
  | VideoLessonContent
  | AudioLessonContent
  | ArticleLessonContent;

export interface Lesson {
  createdAt: string;
  description: string | null;
  id: number;
  isCompleted: boolean;
  lessonAttachments: LessonAttachment[];
  lessonContents: LessonContent[];
  lessonOrder: number;
  title: string;
}

export interface Section {
  createdAt: string;
  id: number;
  lessons: Lesson[];
  name: string;
  sectionOrder: number;
}

export interface Course {
  bannerImage: string;
  completionPercentage: number;
  createdAt: string;
  description: string | null;
  id: number;
  externalId: string;
  name: string;
  nextLessonId: number;
  sections: Section[];
  status: "published" | string;
  tags: string[] | null;
  totalLessons: number;
  totalSections: number;
}

export interface CourseResponse {
  success: boolean;
  data: Course;
}

export interface LessonResponse {
  success: boolean;
  data: Lesson;
}

export interface LiveClass {
  createdAt: string;
  endsAt: string;
  eventUrl: string;
  id: number;
  isRegistered: boolean;
  numOfParticipants: number;
  participants: string[];
  startAt: string;
  topic: string;
}

export interface LiveClassResponse {
  success: boolean;
  data: LiveClass;
}

export interface LiveClassesResponse {
  success: boolean;
  total: number;
  data: {
    liveClasses: LiveClass[];
  };
}

// Profile API Types
export interface UserProfile {
  id: number;
  email: string;
  firstName: string | null;
  lastName: string | null;
  age: number | null;
  avatarUrl: string | null;
  bio: string | null;
  gender: string | null;
  interestedTopics: string[] | null;
  phoneNumber: string | null;
  role: string;
  organizationId: number;
  isActivated: boolean;
  createdAt: string;
  updatedAt: string;
  invitedAt: string | null;
}

export interface ProfileResponse {
  success: boolean;
  data: UserProfile;
}

export interface ProfileByIdResponse {
  avatarUrl: string;
  email: string;
  firstName: string;
  id: number;
  lastName: string;
  role: string;
}

export interface Notification {
  id: number;
  title: string;
  body: string;
  isRead: boolean;
  createdAt: string;
  data: {
    link: string;
  };
}

export interface NotificationsResponse {
  success: boolean;
  data: {
    notifications: Notification[];
    total: number;
  };
}

export interface StreamNotification {
  id: string;
  actor: any;
  verb: string;
  object: any;
  target?: any;
  time: string;
  foreign_id?: string;
  is_read?: boolean;
  is_seen?: boolean;
}
