// Stream Notification Types
export interface StreamNotificationGroup {
  id: string;
  verb: string;
  activities: StreamActivity[];
  activity_count: number;
  actor_count: number;
  created_at: string;
  updated_at: string;
  group: string;
  is_read?: boolean;
  is_seen?: boolean;
}

export interface StreamActivity {
  id: string;
  actor: StreamActor | string;
  verb: string;
  object?: any;
  target?: any;
  time: string;
  foreign_id?: string;
  extra_context?: any;
}

export interface StreamActor {
  id: string;
  data: {
    name: string;
    avatar?: string;
    username?: string;
  };
}

// UI Notification Types
export interface UINotification {
  id: string;
  verb: 'post' | 'like' | 'comment' | 'follow' | 'arrived' | 'generic';
  actors: {
    count: number;
    names: string[];
    avatars: string[];
  };
  createdAt: string;
  isRead: boolean;
  message?: string;
  activityCount?: number;
  post?: {
    message?: string;
    images?: string[];
  };
  targetPost?: {
    message?: string;
    images?: string[];
  };
  commentPreview?: string;
}

export interface PostNotification extends UINotification {
  verb: 'post';
  post: {
    message?: string;
    images?: string[];
  };
  activityCount: number;
}

export interface LikeNotification extends UINotification {
  verb: 'like';
  targetPost: {
    message?: string;
    images?: string[];
  };
}

export interface CommentNotification extends UINotification {
  verb: 'comment';
  targetPost: {
    message?: string;
    images?: string[];
  };
  commentPreview?: string;
}

export interface FollowNotification extends UINotification {
  verb: 'follow';
}

export interface ArrivedNotification extends UINotification {
  verb: 'arrived';
  message: string;
}

export interface GenericNotification extends UINotification {
  verb: 'generic';
  message?: string;
}
