import type {
  StreamNotificationGroup,
  StreamActivity,
  StreamActor,
  UINotification,
} from './types';

export function transformStreamNotification(
  group: StreamNotificationGroup
): UINotification {
  const { activities, activity_count, actor_count, verb, is_read } = group;
  
  // Extract actors information
  const actors = extractActors(activities);
  
  // Get the most recent activity
  const latestActivity = activities[0];
  
  // Base notification structure
  const baseNotification = {
    id: group.id,
    actors,
    createdAt: group.created_at,
    isRead: is_read || false,
    activityCount: activity_count,
  };

  // Transform based on verb
  switch (verb) {
    case 'post':
      return {
        ...baseNotification,
        verb: 'post',
        post: {
          message: extractMessage(latestActivity.object) || extractMessage(latestActivity) || '',
          images: extractImages(latestActivity) || extractImages(latestActivity.object) || [],
        },
      };

    case 'like':
      return {
        ...baseNotification,
        verb: 'like',
        targetPost: {
          message: extractMessage(latestActivity.target) || '',
          images: extractImages(latestActivity.target),
        },
      };

    case 'comment':
      return {
        ...baseNotification,
        verb: 'comment',
        targetPost: {
          message: extractMessage(latestActivity.target) || '',
          images: extractImages(latestActivity.target),
        },
        commentPreview: extractMessage(latestActivity.object) || '',
      };

    case 'follow':
      return {
        ...baseNotification,
        verb: 'follow',
      };

    case 'arrived':
      return {
        ...baseNotification,
        verb: 'arrived',
        message: extractMessage(latestActivity.object) || 'Someone arrived',
      };

    default:
      return {
        ...baseNotification,
        verb: 'generic',
        message: extractMessage(latestActivity.object) || extractMessage(latestActivity) || `New ${verb} activity`,
      };
  }
}

function extractActors(activities: StreamActivity[]): UINotification['actors'] {
  const actorMap = new Map<string, { name: string; avatar: string }>();
  
  activities.forEach((activity) => {
    try {
      const actor = activity.actor;
      
      if (!actor) {
        console.warn('Activity missing actor:', activity);
        return;
      }
      
      const actorId = typeof actor === 'string' ? actor : actor.id;
      
      if (!actorMap.has(actorId)) {
        let actorData: { name: string; avatar: string };
        
        if (typeof actor === 'string') {
          actorData = { name: actor, avatar: '' };
        } else {
          // Safely access nested properties
          const actorObj = actor as StreamActor;
          const data = actorObj.data || {};
          
          actorData = {
            name: data.name || data.username || data.display_name || actorObj.id || 'Unknown',
            avatar: data.avatar || data.profileImage || data.profile_image || data.image || '',
          };
        }
        
        actorMap.set(actorId, actorData);
      }
    } catch (error) {
      console.error('Error extracting actor from activity:', activity, error);
    }
  });

  const actorsList = Array.from(actorMap.values());
  
  return {
    count: actorsList.length,
    names: actorsList.map(a => a.name),
    avatars: actorsList.map(a => a.avatar).filter(Boolean),
  };
}

function extractMessage(object: any): string {
  if (!object) return '';
  
  // Try various possible message fields
  return object.message || 
         object.text || 
         object.content || 
         object.body || 
         object.description || 
         '';
}

function extractImages(object: any): string[] {
  if (!object) return [];
  
  // Check for images array
  if (Array.isArray(object.images)) {
    return object.images.filter((img: any) => typeof img === 'string');
  }
  
  // Check for image property
  if (typeof object.image === 'string') {
    return [object.image];
  }
  
  // Check for attachments
  if (Array.isArray(object.attachments)) {
    return object.attachments
      .filter((att: any) => att.type === 'image' && (att.image_url || att.url))
      .map((att: any) => att.image_url || att.url);
  }
  
  // Check for media array
  if (Array.isArray(object.media)) {
    return object.media
      .filter((m: any) => m.type === 'image' && m.url)
      .map((m: any) => m.url);
  }
  
  return [];
}

export function groupNotificationsByDate(
  notifications: UINotification[]
): Record<string, UINotification[]> {
  const groups: Record<string, UINotification[]> = {};
  
  notifications.forEach((notification) => {
    const date = new Date(notification.createdAt);
    const dateLabel = getDateLabel(date);
    
    if (!groups[dateLabel]) {
      groups[dateLabel] = [];
    }
    
    groups[dateLabel].push(notification);
  });
  
  return groups;
}

function getDateLabel(date: Date): string {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days === 0) {
    return 'Today';
  } else if (days === 1) {
    return 'Yesterday';
  } else if (days < 7) {
    return date.toLocaleDateString(undefined, { weekday: 'long' });
  } else {
    return date.toLocaleDateString(undefined, { 
      month: 'short', 
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  }
}
