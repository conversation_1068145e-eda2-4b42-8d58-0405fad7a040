import { useState, useEffect } from "react";
import {
  StyleSheet,
  TextInput,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ImageBackground,
  Image,
} from "react-native";
import { router } from "expo-router";
import SuperTokens from "supertokens-react-native";

import { navigateTo } from "@/utils/navigation";
import { useAppContext } from "@/context/app";
import { GoogleButton } from "@/components/GoogleButton";

export default function SignUp() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailError, setEmailError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [authError, setAuthError] = useState("");

  const { setIsAuthenticated } = useAppContext();

  useEffect(() => {
    // Optional: Check if session exists and redirect if already logged in
    SuperTokens.doesSessionExist().then((exists) => {
      if (exists) {
        console.log("User already logged in, redirecting...");
        router.replace("/(app)/(tabs)");
      }
    });
  }, []);

  const handleSignUp = async () => {
    if (isSubmitting) return;

    // Reset errors
    setEmailError("");
    setPasswordError("");
    setAuthError("");

    setIsSubmitting(true);
    try {
      const response = await fetch(
        process.env.EXPO_PUBLIC_API_DOMAIN + "/auth/signup",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json; charset=utf-8",
          },
          body: JSON.stringify({
            formFields: [
              { id: "email", value: email },
              { id: "password", value: password },
            ],
          }),
        }
      );

      const data = await response.json();

      switch (data.status) {
        case "OK":
          console.log("Signup successful");
          // SuperTokens handles session creation automatically upon successful signup
          setIsAuthenticated(true);
          router.replace("/(app)/(tabs)"); // Redirect to app main screen
          break;
        case "FIELD_ERROR":
          data.formFields.forEach((field: { id: string; error: string }) => {
            if (field.id === "email") {
              setEmailError(field.error);
            } else if (field.id === "password") {
              setPasswordError(field.error);
            }
          });
          break;
        case "EMAIL_ALREADY_EXISTS_ERROR":
          setAuthError("An account with this email already exists.");
          break;
        case "SIGN_UP_NOT_ALLOWED":
          setAuthError(`Sign up not allowed. Support code: ${data.reason}`);
          break;
        case "GENERAL_ERROR":
          setAuthError(
            data.message || "An unexpected error occurred during sign up."
          );
          break;
        default:
          setAuthError("An unexpected sign up error occurred.");
      }
    } catch (e) {
      console.error("Sign up failed:", e instanceof Error ? e.message : e);
      setAuthError("Failed to connect to the server for sign up.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ImageBackground
      source={require("../assets/images/tree-bg.png")} // Reusing the background
      style={styles.backgroundImage}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.container}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <View style={styles.innerContainer}>
          <View style={styles.contentWrapper}>
            <Image
              source={require("../assets/images/icon.png")} // Reusing the logo
              style={styles.logo}
              resizeMode="contain"
            />

            <View style={styles.formContainer}>
              <Text style={styles.title}>Create Account</Text>
              <Text style={styles.subtitle}>Join Sphere today</Text>

              <TextInput
                style={[styles.input, emailError ? styles.inputError : null]}
                placeholder="Email"
                placeholderTextColor="#999"
                value={email}
                onChangeText={setEmail}
                autoCapitalize="none"
                keyboardType="email-address"
                editable={!isSubmitting}
              />
              {emailError ? (
                <Text style={styles.errorText}>{emailError}</Text>
              ) : null}

              <TextInput
                style={[styles.input, passwordError ? styles.inputError : null]}
                placeholder="Password"
                placeholderTextColor="#999"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                editable={!isSubmitting}
              />
              {passwordError ? (
                <Text style={styles.errorText}>{passwordError}</Text>
              ) : null}

              {authError ? (
                <Text style={styles.errorText}>{authError}</Text>
              ) : null}

              <TouchableOpacity
                style={[styles.button, isSubmitting && styles.buttonDisabled]}
                onPress={handleSignUp}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text style={styles.buttonText}>Sign Up</Text>
                )}
              </TouchableOpacity>

              <View style={styles.divider}>
                <View style={styles.dividerLine} />
                <Text style={styles.dividerText}>OR</Text>
                <View style={styles.dividerLine} />
              </View>

              <GoogleButton signup />

              <View style={styles.loginContainer}>
                <Text style={styles.loginText}>Already have an account? </Text>
                <TouchableOpacity onPress={navigateTo.signIn}>
                  <Text style={styles.loginLink}>Sign In</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </ImageBackground>
  );
}

// Reusing and adapting styles from sign-in.tsx
const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  container: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
  },
  innerContainer: {
    flex: 1,
    justifyContent: "center",
    paddingHorizontal: 20,
  },
  contentWrapper: {
    alignItems: "center",
  },
  logo: {
    width: 60,
    height: 60,
    marginBottom: 40,
  },
  formContainer: {
    width: "100%",
    maxWidth: 400,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 8,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 14,
    color: "#ccc",
    marginBottom: 24,
    textAlign: "center",
  },
  input: {
    width: "100%",
    height: 48,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: 8,
    marginBottom: 12,
    paddingHorizontal: 15,
    fontSize: 16,
    color: "#fff",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  inputError: {
    borderColor: "#ff4545",
  },
  button: {
    backgroundColor: "#4B5EAB",
    paddingVertical: 14,
    paddingHorizontal: 15,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 20,
  },
  buttonDisabled: {
    backgroundColor: "rgba(75, 94, 171, 0.5)",
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  errorText: {
    color: "#ff4545",
    fontSize: 12,
    marginTop: -12, // Adjust spacing for errors
    marginBottom: 12,
    alignSelf: "flex-start", // Align error text to the left
    paddingLeft: 2, // Small padding if needed
  },
  loginContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 20,
  },
  loginText: {
    color: "#fff",
  },
  loginLink: {
    color: "#fff",
    fontWeight: "bold",
  },
  divider: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 20,
    marginBottom: 8,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
  },
  dividerText: {
    color: "#ccc",
    paddingHorizontal: 10,
    fontSize: 14,
  },
});
