import { useEffect, useState, useMemo } from "react";
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  ScrollView,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
} from "react-native";
import { Stack, router, useLocalSearchParams } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { useSafeAreaInsets } from "react-native-safe-area-context";

import { useGroupCohorts, useJoinCohort, useMyGroups } from "@/lib/api/queries";
import { useAppContext } from "@/context/app";

const formatDateRange = (startDate: string, endDate: string | null) => {
  const start = new Date(startDate);
  const startFormatted = `${start.getDate()} ${start.toLocaleString("default", {
    month: "short",
  })} ${start.getFullYear()}`;

  if (!endDate) {
    return startFormatted;
  }

  const end = new Date(endDate);
  const endFormatted = `${end.getDate()} ${end.toLocaleString("default", {
    month: "short",
  })} ${end.getFullYear()}`;

  return `${startFormatted} - ${endFormatted}`;
};

export default function CohortSelection() {
  const insets = useSafeAreaInsets();
  const { setGroupId, setCohortId } = useAppContext();
  const { groupId } = useLocalSearchParams<{ groupId: string }>();

  const { isPending, error, data } = useGroupCohorts(groupId);
  const { data: myGroupsData, isPending: myGroupsPending } = useMyGroups();

  const [selectedCohortId, setSelectedCohortId] = useState<string | null>(null);

  const {
    mutate: joinCohort,
    isPending: isJoiningCohort,
    isSuccess: isJoinedCohort,
    isError: isJoiningCohortError,
    error: joinCohortError,
  } = useJoinCohort();

  // Handle redirect on successful join
  useEffect(() => {
    if (isJoinedCohort) {
      // set group and cohort id to be the one user just joined
      setGroupId(groupId);
      setCohortId(selectedCohortId);
      // Redirect to join cohort success page
      router.replace(`/groups/${groupId}/join-cohort-success`);
    }
  }, [isJoinedCohort]);

  // Handle error
  useEffect(() => {
    if (isJoiningCohortError && joinCohortError) {
      Alert.alert(
        "Error Joining Cohort",
        joinCohortError.message ||
          "There was an error joining the cohort. Please try again."
      );
    }
  }, [isJoiningCohortError, joinCohortError]);

  const cohorts = useMemo(() => {
    const group = myGroupsData?.groups?.byId?.[groupId];
    let cohorts = [];

    // if group is not found, meaning the user is not joined any cohort of this group
    // so we should show all cohorts
    if (!group) {
      cohorts =
        data?.data?.cohorts?.map((cohort) => ({
          ...cohort,
          isJoined: false,
        })) ?? [];
    } else {
      cohorts = data?.data?.cohorts?.map((cohort) => ({
        ...cohort,
        isJoined: group.joinedCohorts.some((c) => c.id === cohort.id),
      }));
    }

    // only show non-default cohorts
    return cohorts.filter((cohort) => !cohort.isDefault);
  }, [data?.data?.cohorts, myGroupsData?.groups?.byId, groupId]);

  if (isPending || myGroupsPending) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color="#fff" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.errorText}>Error: {error.message}</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container]}>
      <Stack.Screen
        options={{
          headerShown: false,
          presentation: "modal",
        }}
      />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => router.back()}
        >
          <MaterialIcons name="close" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Join New Cohort</Text>
        <View style={{ width: 40 }} />
      </View>

      <ScrollView
        style={styles.contentContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {cohorts.map((cohort) => (
          <Pressable
            key={cohort.id}
            style={({ pressed }) => [
              styles.cohortCard,
              pressed && styles.cohortCardPressed,
              selectedCohortId === cohort.externalId &&
                styles.cohortCardSelected,
              cohort.isJoined && styles.cohortCardDisabled,
            ]}
            disabled={cohort.isJoined}
            onPress={() => {
              if (selectedCohortId === cohort.id.toString()) {
                setSelectedCohortId(null);
              } else {
                setSelectedCohortId(cohort.externalId);
              }
            }}
          >
            <View>
              <Text style={styles.cohortName}>{cohort.name}</Text>
              <Text style={styles.cohortDate}>
                {formatDateRange(cohort.startDate, cohort.endDate)}
              </Text>
            </View>

            <View style={styles.rightContent}>
              {cohort.isJoined ? (
                <View style={[styles.priceTag, styles.joinedTag]}>
                  <Text style={styles.priceText}>Joined</Text>
                </View>
              ) : (
                <View
                  style={[
                    styles.priceTag,
                    cohort.name.includes("VIP") && {
                      backgroundColor: "#6366F1",
                    },
                  ]}
                >
                  <Text style={styles.priceText}>
                    {cohort.name.includes("VIP") ? "$99 / year" : "Free"}
                  </Text>
                </View>
              )}
            </View>
          </Pressable>
        ))}
      </ScrollView>
      <View style={[styles.footer, { paddingBottom: insets.bottom + 16 }]}>
        <TouchableOpacity
          style={[
            styles.joinButton,
            (!selectedCohortId || isJoiningCohort) && styles.joinButtonDisabled,
          ]}
          disabled={!selectedCohortId || isJoiningCohort}
          onPress={() => {
            if (selectedCohortId) {
              joinCohort({
                groupId: groupId,
                cohortId: selectedCohortId,
              });
            }
          }}
        >
          <Text style={styles.joinButtonText}>
            {isJoiningCohort ? "Joining..." : "Join Cohort"}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1A1C1E",
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    color: "#fff",
    fontSize: 16,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 24,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#2C2E33",
    alignItems: "center",
    justifyContent: "center",
  },
  headerTitle: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  contentContainer: {},
  scrollContent: {
    padding: 16,
    gap: 16,
  },
  cohortCard: {
    backgroundColor: "#2C2E33",
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: "#444",
  },
  cohortCardPressed: {
    opacity: 0.8,
  },
  cohortCardSelected: {
    backgroundColor: "#3C3E43",
    borderWidth: 1,
    borderColor: "#6366F1",
  },
  cohortCardDisabled: {
    opacity: 0.4,
  },
  cohortName: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 4,
  },
  cohortDate: {
    color: "#9B9B9B",
    fontSize: 14,
  },
  rightContent: {
    alignItems: "flex-end",
    justifyContent: "center",
  },
  priceTag: {
    backgroundColor: "#EF5252",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 100,
  },
  joinedTag: {
    backgroundColor: "#232428",
  },
  priceText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  footer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#2C2E33",
  },
  joinButton: {
    backgroundColor: "#6366F1",
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  joinButtonDisabled: {
    backgroundColor: "#2C2E33",
    opacity: 0.6,
  },
  joinButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});
