import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { queryClient } from "@/lib/providers/query-client";

export default function JoinCohortSuccess() {
  const { id } = useLocalSearchParams<{ id: string }>();
  // Invalidate and refetch the groups query
  queryClient.invalidateQueries({ queryKey: ["my", "groups"] });

  return (
    <View style={styles.container}>
      <MaterialIcons
        name="check-circle"
        size={80}
        color="#4ADE80"
        style={{ marginBottom: 24 }}
      />
      <Text style={styles.title}>Joined Successfully!</Text>
      <Text style={styles.subtitle}>
        You have joined the cohort. Welcome aboard!
      </Text>
      <TouchableOpacity
        style={styles.button}
        onPress={() => router.replace("/")}
      >
        <Text style={styles.buttonText}>Go to Home</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1A1C1E",
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  title: {
    color: "#fff",
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 12,
    textAlign: "center",
  },
  subtitle: {
    color: "#B0B0B0",
    fontSize: 16,
    marginBottom: 32,
    textAlign: "center",
  },
  button: {
    backgroundColor: "#6366F1",
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: "center",
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});
