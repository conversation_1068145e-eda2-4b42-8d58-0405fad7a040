import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Linking,
  ActivityIndicator,
} from "react-native";
import { useLocalSearchParams, router } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";

import { useLiveClass, useRegisterLiveClass } from "@/lib/api/queries";

export default function EventDetails() {
  const { groupId, cohortId, moduleId, id } = useLocalSearchParams<{
    groupId: string;
    cohortId: string;
    moduleId: string;
    id: string;
  }>();

  const { data: liveClass } = useLiveClass(groupId, cohortId, moduleId, id);

  const startTime = new Date(liveClass?.data.startAt!);
  const endTime = new Date(liveClass?.data.endsAt!);
  const timeString = `${startTime.toLocaleString("en-US", {
    hour: "numeric",
    minute: "numeric",
    hour12: true,
  })} - ${endTime.toLocaleString("en-US", {
    hour: "numeric",
    minute: "numeric",
    hour12: true,
  })}`;

  const dateString = startTime.toLocaleString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });

  const { mutateAsync: registerLiveClass, isPending: isRegistering } =
    useRegisterLiveClass();

  const isEventOver = new Date() > endTime;

  const handleBack = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.replace("/");
    }
  };

  const handleRegisterEvent = () => {
    if (!isEventOver && liveClass?.data.eventUrl) {
      registerLiveClass({
        groupId: groupId!,
        cohortId: cohortId!,
        moduleId: moduleId!,
        liveClassId: id!,
      });
    }
  };

  const handleJoinEvent = () => {
    if (!isEventOver && liveClass?.data.eventUrl) {
      liveClass?.data.eventUrl && Linking.openURL(liveClass.data.eventUrl);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.closeButton} onPress={handleBack}>
          <MaterialIcons name="close" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Join Live Class</Text>
        <View style={{ width: 40 }} />
      </View>

      <View style={styles.content}>
        <Text style={styles.topic}>{liveClass?.data.topic}</Text>

        <View style={styles.infoSection}>
          <Text style={styles.sectionLabel}>Time</Text>
          <Text style={styles.sectionValue}>{timeString}</Text>
        </View>

        <View style={styles.infoSection}>
          <Text style={styles.sectionLabel}>Date</Text>
          <Text style={styles.sectionValue}>{dateString}</Text>
        </View>

        <View style={styles.infoSection}>
          <Text style={styles.sectionLabel}>Host by</Text>
          <Text style={styles.sectionValue}>Felix Yang</Text>
        </View>

        <View style={styles.participantsSection}>
          <Text style={styles.sectionLabel}>Participants</Text>
          <View style={styles.participantsInfo}>
            <View style={styles.avatarsContainer}>
              {[1, 2, 3, 4, 5].map((_, index) => (
                <View
                  key={index}
                  style={[styles.avatar, index > 0 && styles.avatarOverlap]}
                />
              ))}
            </View>
            <Text style={styles.participantsCount}>
              {liveClass?.data.numOfParticipants} Attending
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.footer}>
        {isEventOver ? (
          <TouchableOpacity
            style={[styles.rsvpButton, styles.disabledButton]}
            disabled={true}
          >
            <Text style={[styles.rsvpButtonText, styles.disabledButtonText]}>
              Live Class is over
            </Text>
          </TouchableOpacity>
        ) : liveClass?.data.isRegistered ? (
          <TouchableOpacity
            style={[styles.rsvpButton, styles.joinButton]}
            onPress={handleJoinEvent}
            disabled={!liveClass?.data.eventUrl}
          >
            <Text style={styles.rsvpButtonText}>Go to the Live Class</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.rsvpButton, isRegistering && styles.disabledButton]}
            onPress={handleRegisterEvent}
            disabled={isRegistering}
          >
            {isRegistering ? (
              <ActivityIndicator color="#FFFFFF" size="small" />
            ) : (
              <Text style={styles.rsvpButtonText}>RSVP</Text>
            )}
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1A1C1E",
  },
  header: {
    padding: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 24,
  },
  headerTitle: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "600",
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#2C2E33",
    alignItems: "center",
    justifyContent: "center",
  },
  content: {
    flex: 1,
    padding: 24,
  },
  topic: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "600",
    marginBottom: 32,
  },
  infoSection: {
    marginBottom: 24,
  },
  sectionLabel: {
    color: "#9B9B9B",
    fontSize: 14,
    marginBottom: 4,
  },
  sectionValue: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
  },
  participantsSection: {
    marginTop: 8,
  },
  participantsInfo: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  avatarsContainer: {
    flexDirection: "row",
    marginRight: 12,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#666",
    borderWidth: 2,
    borderColor: "#1A1C1E",
  },
  avatarOverlap: {
    marginLeft: -16,
  },
  participantsCount: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "500",
  },
  footer: {
    padding: 24,
    borderTopWidth: 1,
    borderTopColor: "#2C2E33",
  },
  rsvpButton: {
    backgroundColor: "#EF5252",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  joinButton: {
    backgroundColor: "#4CAF50",
  },
  rsvpButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  disabledButton: {
    backgroundColor: "#2C2E33",
  },
  disabledButtonText: {
    color: "#9B9B9B",
  },
});
