import React, { use<PERSON>allback, useMemo } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  SectionList,
  Image,
  ActivityIndicator,
} from "react-native";
import { router } from "expo-router";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import Octicons from "@expo/vector-icons/Octicons";
import { X } from "lucide-react-native";

import { useMyGroups } from "@/lib/api/queries";
import { useAppContext } from "@/context/app";

type SectionItem = {
  id: string;
  name: string;
  type: "group" | "cohort";
  bannerImage?: string;
  groupId?: string;
};

const GroupSelection: React.FC = () => {
  // Access the group context
  const { setGroupId, setCohortId } = useAppContext();

  // Fetch groups
  const { data: groupsData, isLoading, error: groupsError } = useMyGroups();

  // Memoize the sections data to prevent unnecessary re-renders
  const sections = useMemo(() => {
    if (!groupsData?.success) {
      return [];
    }

    return groupsData?.groups.order.map((id) => {
      const group = groupsData?.groups.byId[id];
      // Create section data with group and its cohorts
      const sectionItems: SectionItem[] = [
        // Add the group itself
        {
          id: group.externalId,
          name: group.name,
          type: "group",
        },
      ];

      // Add all cohorts for this group
      group.joinedCohorts.forEach((cohort) => {
        sectionItems.push({
          id: cohort.externalId,
          name: cohort.name,
          type: "cohort",
          groupId: group.externalId,
          bannerImage: group.bannerImage,
        });
      });

      return {
        title: group.name,
        data: sectionItems,
      };
    });
  }, [groupsData?.groups.order]);

  // Handle item press with useCallback to prevent unnecessary re-renders
  const handleItemPress = useCallback(
    (item: { id: number; type: "group" | "cohort"; groupId?: number }) => {
      // Set the selected group ID in the context
      if (item.type === "cohort" && item.groupId) {
        setGroupId(item.groupId.toString());
        setCohortId(item.id.toString());
      }

      // Navigate back to the home screen after selection
      router.back();
    },
    [setGroupId]
  );

  // Render a simple separator between sections
  const renderSectionHeader = useCallback(
    () => <View style={styles.sectionSeparator} />,
    []
  );

  const renderItem = useCallback(
    ({ item }: { item: SectionItem }) => {
      // Render different components based on item type
      if (item.type === "group") {
        // Non-touchable view for group items
        return (
          <View style={styles.item}>
            <Text style={styles.groupItemText}>{item.name}</Text>
          </View>
        );
      } else {
        // Touchable component for cohort items
        return (
          <TouchableOpacity
            style={[styles.item, styles.cohortItem]}
            onPress={() => handleItemPress(item)}
          >
            {item.bannerImage ? (
              <Image
                source={{ uri: item.bannerImage }}
                style={styles.groupAvatar}
              />
            ) : (
              <View style={styles.cohortIconContainer}>
                <MaterialIcons name="group" size={20} color="#fff" />
              </View>
            )}
            <Text style={styles.cohortItemText}>{item.name}</Text>
          </TouchableOpacity>
        );
      }
    },
    [handleItemPress]
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.indicator}></View>
      <View style={styles.header}>
        <View style={styles.headerSwitchGroup}>
          <Octicons name="arrow-switch" size={20} color="#fff" />
          <Text style={styles.headerTitle}>Switch Group</Text>
        </View>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.closeButton}
        >
          <X size={20} color="#fff" />
        </TouchableOpacity>
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#EF5252" />
          <Text style={styles.loadingText}>Loading groups...</Text>
        </View>
      ) : groupsError ? (
        <View style={styles.errorContainer}>
          <MaterialIcons name="error-outline" size={48} color="#EF5252" />
          <Text style={styles.errorText}>{groupsError.message}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => {
              // The query will automatically refetch when enabled
            }}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <SectionList
          sections={sections}
          keyExtractor={(item) => `${item.type}-${item.id}`}
          renderItem={renderItem}
          renderSectionHeader={renderSectionHeader}
          stickySectionHeadersEnabled={true}
          contentContainerStyle={styles.listContent}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#151515",
    borderTopWidth: 1,
    borderTopColor: "#1A1A1A",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 10,
  },
  headerSwitchGroup: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  closeButton: {
    backgroundColor: "#2A2A2A",
    padding: 8,
    borderRadius: 18,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 14,
    fontWeight: "700",
    color: "#fff",
  },
  listContent: {
    paddingBottom: 10,
  },
  sectionSeparator: {
    height: 1,
    backgroundColor: "#1A1A1A",
    marginVertical: 8,
  },
  sectionHeader: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "700",
    color: "#fff",
  },
  item: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  cohortItem: {
    // paddingLeft: 32, // Indent cohort items
  },
  groupAvatar: {
    width: 32,
    height: 32,
    borderRadius: 5,
    marginRight: 12,
  },
  cohortIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#333",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  groupItemText: {
    fontSize: 16,
    color: "#fff",
    flex: 1,
    fontWeight: "700",
  },
  cohortItemText: {
    fontSize: 16,
    color: "#ffffff",
    opacity: 0.7,
    flex: 1,
  },
  badgeContainer: {
    backgroundColor: "#EF5252",
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: "700",
    color: "#fff",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: "#fff",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 24,
  },
  errorText: {
    marginTop: 12,
    fontSize: 16,
    color: "#fff",
    textAlign: "center",
  },
  retryButton: {
    marginTop: 24,
    backgroundColor: "#EF5252",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: "700",
    color: "#fff",
  },
  indicator: {
    height: 10,
    backgroundColor: "#1A1A1A",
    width: 100,
    borderRadius: 10,
    alignSelf: "center",
    marginTop: 12,
  },
});

export default GroupSelection;
