import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import { CheckCircle } from "lucide-react-native";
import { useCourse } from "@/lib/api/queries";

export default function CompleteLessonSuccess() {
  const { courseId, sectionId, lessonId } = useLocalSearchParams<{
    courseId: string;
    sectionId: string;
    lessonId: string;
  }>();

  const { data: courseData } = useCourse(courseId);
  const currentLessonId = Number(lessonId);
  const currentSectionId = Number(sectionId);

  // Find the next lesson
  const findNextLesson = () => {
    if (!courseData?.data) return null;

    // Sort sections by order
    const sortedSections = [...courseData.data.sections].sort(
      (a, b) => a.sectionOrder - b.sectionOrder
    );

    // Find current section index
    const currentSectionIndex = sortedSections.findIndex(
      (section) => section.id === currentSectionId
    );

    // Find current lesson index in current section
    const currentSection = sortedSections[currentSectionIndex];
    const currentLessonIndex = currentSection.lessons.findIndex(
      (lesson) => lesson.id === currentLessonId
    );

    // Check if there's a next lesson in current section
    if (currentLessonIndex < currentSection.lessons.length - 1) {
      const nextLesson = currentSection.lessons[currentLessonIndex + 1];
      return {
        sectionId: currentSectionId,
        lessonId: nextLesson.id,
      };
    }

    // Check if there's a next section
    if (currentSectionIndex < sortedSections.length - 1) {
      const nextSection = sortedSections[currentSectionIndex + 1];
      if (nextSection.lessons.length > 0) {
        return {
          sectionId: nextSection.id,
          lessonId: nextSection.lessons[0].id,
        };
      }
    }

    return null;
  };

  const nextLesson = findNextLesson();

  return (
    <View style={styles.container}>
      <CheckCircle size={80} color="#4ADE80" style={{ marginBottom: 24 }} />
      <Text style={styles.title}>Lesson Completed!</Text>
      <Text style={styles.subtitle}>
        Great job! You've successfully completed this lesson.
      </Text>
      <View style={styles.buttonContainer}>
        {nextLesson ? (
          <TouchableOpacity
            style={[styles.button, styles.nextButton]}
            onPress={() =>
              router.replace(
                `/courses/${courseId}/sections/${nextLesson.sectionId}/lessons/${nextLesson.lessonId}`
              )
            }
          >
            <Text style={styles.buttonText}>Next Lesson</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.button, styles.nextButton]}
            onPress={() => router.back()}
          >
            <Text style={styles.buttonText}>Back to Course</Text>
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={[styles.button, styles.backButton]}
          onPress={() =>
            router.replace(
              `/courses/${courseId}/sections/${sectionId}/lessons/${lessonId}`
            )
          }
        >
          <Text style={styles.buttonText}>Back to Lesson</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1A1C1E",
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  title: {
    color: "#fff",
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 12,
    textAlign: "center",
  },
  subtitle: {
    color: "#B0B0B0",
    fontSize: 16,
    marginBottom: 32,
    textAlign: "center",
  },
  buttonContainer: {
    width: "100%",
    gap: 12,
  },
  button: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: "center",
  },
  backButton: {
    backgroundColor: "#2D2D2D",
  },
  nextButton: {
    backgroundColor: "#6366F1",
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});
