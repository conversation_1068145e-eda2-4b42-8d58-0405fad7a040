import {
  View,
  Text,
  StyleSheet,
  Pressable,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  ScrollView,
} from "react-native";
import { router, Link, useLocalSearchParams } from "expo-router";
import { useEvent, useEventListener } from "expo";
import { useVideoPlayer, VideoView, type VideoSource } from "expo-video";
import {
  Link2,
  File,
  List,
  Check,
  ArrowLeft,
  MoreHorizontal,
  Play,
  Clock,
  RotateCcw,
} from "lucide-react-native";
import { useState, useCallback } from "react";

import { useLesson, useCompleteLesson } from "@/lib/api/queries";

// ------------------------------
// Helper Component to render a single lesson content item
// ------------------------------
type LessonContent = {
  id?: string;
  contentType: "video" | "audio" | "article" | "content";
  metadata: {
    url?: string;
    content?: string;
  } & Record<string, any>;
};

interface MediaContentItemProps {
  content: LessonContent;
}

function MediaContentItem({ content }: MediaContentItemProps) {
  // Determine if the content is a video that is still processing (e.g. Mux/Mediadelivery placeholder)
  const isVideoProcessing =
    content.contentType === "video" &&
    !!content?.metadata?.url?.startsWith("https://iframe.mediadelivery.net");

  // Always provide a (possibly empty) source to satisfy the hook's typing
  const source = (
    isVideoProcessing ? "" : content?.metadata?.url || ""
  ) as VideoSource;

  const player = useVideoPlayer(source, (p) => {
    p.showNowPlayingNotification = true;
    // Auto-play when a valid source is present
    if (source) {
      p.play();
    }
  });

  const { isPlaying } = useEvent(player, "playingChange", {
    isPlaying: player.playing,
  });

  // Track playback end to show replay overlay
  const [ended, setEnded] = useState(false);

  useEventListener(player, "playToEnd", () => {
    setEnded(true);
  });

  if (content.contentType === "video") {
    return (
      <View style={styles.videoContainer}>
        {isVideoProcessing ? (
          <View style={styles.processingContainer}>
            <Clock size={48} color="#6366F1" />
            <Text style={styles.processingTitle}>Video Processing</Text>
            <Text style={styles.processingSubtitle}>
              This video will be available soon
            </Text>
          </View>
        ) : (
          <>
            <VideoView
              style={styles.video}
              player={player}
              allowsFullscreen
              allowsPictureInPicture
              nativeControls
              contentFit="contain"
            />
            {ended && (
              <Pressable
                style={styles.replayButton}
                onPress={async () => {
                  try {
                    await player.replay();
                    player.play();
                    setEnded(false);
                  } catch (error) {
                    console.error("Error replaying video:", error);
                  }
                }}
              >
                <RotateCcw size={24} color="white" />
              </Pressable>
            )}
          </>
        )}
      </View>
    );
  }

  if (content.contentType === "audio") {
    return (
      <View style={styles.audioContainer}>
        <VideoView
          style={styles.audio}
          player={player}
          allowsFullscreen={false}
          allowsPictureInPicture={false}
          nativeControls
          contentFit="fill"
        />
      </View>
    );
  }

  // Article / text content
  if (content.contentType === "article") {
    return (
      <View style={styles.articleContainer}>
        <Text style={styles.articleContent}>{content.metadata.content}</Text>
      </View>
    );
  }

  // Unsupported content type
  return null;
}

// ------------------------------
// Utility to split contents by type for ordered rendering
// ------------------------------
function splitContents(contents: LessonContent[] | undefined) {
  const video = contents?.find((c) => c.contentType === "video");
  const audio = contents?.find((c) => c.contentType === "audio");
  const articles = contents?.filter((c) =>
    ["article", "content"].includes(c.contentType)
  );

  return { video, audio, articles } as const;
}

function LessonScreen() {
  const { courseId, sectionId, lessonId } = useLocalSearchParams();

  const { mutate: completeLesson } = useCompleteLesson();

  const handleOpenLink = async (url: string) => {
    try {
      await Linking.openURL(url);
    } catch (error) {
      console.error("Error opening URL:", error);
      Alert.alert("Error", "Could not open the URL");
    }
  };

  const { data: lessonData, isLoading } = useLesson(
    courseId as string,
    sectionId as string,
    lessonId as string
  );

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text>Loading...</Text>
      </View>
    );
  }

  const { video, audio, articles } = splitContents(
    lessonData?.data?.lessonContents
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Pressable onPress={() => router.back()}>
          <ArrowLeft size={24} color="white" />
        </Pressable>
        <Pressable onPress={() => {}}>
          <MoreHorizontal size={24} color="white" />
        </Pressable>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 1. Video */}
        {video && <MediaContentItem content={video} />}

        <View style={styles.lessonContainer}>
          {/* 2. Title */}
          <View style={styles.lessonInfo}>
            <Text style={styles.lessonNumber}>{lessonData?.data.title}</Text>
          </View>

          {/* 3. Description (collapsible) */}
          {lessonData?.data.description ? (
            <CollapsibleDescription text={lessonData.data.description} />
          ) : null}

          {/* 4. Audio */}
          {audio && <MediaContentItem content={audio} />}

          {/* 5. Article(s) */}
          {articles?.map((article, idx) => (
            <MediaContentItem key={idx} content={article} />
          ))}

          {(lessonData?.data.lessonAttachments || []).length > 0 && (
            <View style={styles.attachmentsContainer}>
              <Text style={styles.attachmentsTitle}>Attachments</Text>

              {lessonData?.data.lessonAttachments.map((attachment) => (
                <Pressable
                  key={attachment.id}
                  style={styles.attachment}
                  onPress={() => handleOpenLink(attachment.url)}
                >
                  <View
                    style={[
                      styles.attachmentIconContainer,
                      attachment.attachmentType === "document"
                        ? styles.documentIconContainer
                        : styles.linkIconContainer,
                    ]}
                  >
                    {attachment.attachmentType === "document" ? (
                      <File size={20} color="#ffffff" />
                    ) : (
                      <Link2 size={20} color="#ffffff" />
                    )}
                  </View>
                  <View style={styles.attachmentInfo}>
                    <Text style={styles.attachmentTitle}>
                      {attachment.title}
                    </Text>
                    <Text style={styles.attachmentMeta}>{attachment.url}</Text>
                  </View>
                </Pressable>
              ))}
            </View>
          )}
        </View>
      </ScrollView>
      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <View style={styles.actionContainer}>
          <View style={styles.rightActions}>
            <Link
              href={`/courses/${courseId}/sections/${sectionId}/lessons/${lessonId}/toc`}
              style={styles.tocLink}
            >
              <List size={24} color="#5C5C5C" />
            </Link>
          </View>
          <Pressable
            style={[
              styles.completeButton,
              lessonData?.data?.isCompleted && styles.completedButton,
            ]}
            onPress={() => {
              if (lessonData?.data?.isCompleted) return;

              completeLesson(
                {
                  courseId: courseId as string,
                  sectionId: sectionId as string,
                  lessonId: lessonId as string,
                },
                {
                  onSuccess: () => {
                    router.replace(
                      `/courses/${courseId}/sections/${sectionId}/lessons/${lessonId}/complete-success`
                    );
                  },
                  onError: (error) => {
                    console.error("Error completing lesson:", error);
                  },
                }
              );
            }}
          >
            <Check size={20} color="white" />
            <Text style={styles.completeButtonText}>
              {lessonData?.data?.isCompleted ? "Completed" : "Mark as Complete"}
            </Text>
          </Pressable>
        </View>
      </View>
    </View>
  );
}

// ------------------------------
// CollapsibleDescription Component
// ------------------------------
function CollapsibleDescription({ text }: { text: string }) {
  const [expanded, setExpanded] = useState(false);
  const [isOverflow, setIsOverflow] = useState(false);
  const [measured, setMeasured] = useState(false);

  const toggle = () => setExpanded((prev: boolean) => !prev);

  const onTextLayout = useCallback(
    (e: any) => {
      // Only measure once
      if (!measured) {
        const lines = e?.nativeEvent?.lines ?? [];
        if (lines.length > 2) {
          setIsOverflow(true);
        }
        setMeasured(true);
      }
    },
    [measured]
  );

  return (
    <View style={{ marginBottom: 16 }}>
      <Text
        style={styles.description}
        numberOfLines={!measured || expanded ? undefined : 2}
        onTextLayout={onTextLayout}
      >
        {text}
      </Text>
      {/* Toggle Button */}
      {isOverflow && (
        <Pressable onPress={toggle}>
          <Text style={styles.readMore}>{expanded ? "Less" : "More"}</Text>
        </Pressable>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    paddingTop: 48,
  },
  content: {
    flex: 1,
    paddingTop: 16,
    paddingBottom: 0,
  },
  lessonContainer: {
    padding: 16,
  },
  videoContainer: {
    position: "relative",
    width: "100%",
    aspectRatio: 16 / 9,
    overflow: "hidden",
    backgroundColor: "#000", // Dark background while loading
    marginBottom: 16,
  },
  video: {
    width: "100%",
    height: "100%",
  },
  audioContainer: {
    position: "relative",
    width: "100%",
    height: 80,
    borderRadius: 12,
    overflow: "hidden",
    backgroundColor: "#1A1A1A",
    marginBottom: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  audio: {
    width: "100%",
    height: "100%",
    borderWidth: 1,
    borderColor: "blue",
  },
  playButton: {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: [{ translateX: -21 }, { translateY: -21 }],
    width: 42,
    height: 42,
    borderRadius: 21,
    backgroundColor: "rgba(239, 82, 82, 0.8)",
    justifyContent: "center",
    alignItems: "center",
  },
  replayButton: {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: [{ translateX: -24 }, { translateY: -24 }],
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "rgba(0,0,0,0.6)",
    justifyContent: "center",
    alignItems: "center",
  },
  lessonInfo: {
    gap: 8,
  },
  lessonNumber: {
    color: "#fff",
    fontFamily: "Inter",
    fontSize: 24,
    fontWeight: "700",
    lineHeight: 32,
  },
  lessonSection: {
    color: "#5C5C5C",
    fontFamily: "Inter",
    fontSize: 14,
    fontWeight: "500",
    marginTop: 4,
  },
  description: {
    color: "#9A9A9A",
    fontFamily: "Inter",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 24,
  },
  attachmentsContainer: {
    gap: 1,
  },
  attachmentsTitle: {
    color: "#9A9A9A",
    fontFamily: "Inter",
    fontSize: 12,
    fontWeight: "bold",
    marginBottom: 4,
  },
  attachment: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
  },
  attachmentIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  documentIconContainer: {
    backgroundColor: "#2C2E33",
  },
  linkIconContainer: {
    backgroundColor: "#637381",
  },
  attachmentInfo: {
    flex: 1,
    marginRight: 12,
  },
  attachmentTitle: {
    fontSize: 16,
    fontWeight: "500",
    color: "#FFFFFF",
    marginBottom: 4,
  },
  attachmentMeta: {
    fontSize: 12,
    color: "#6B7280",
    opacity: 0.8,
  },
  bottomActions: {
    padding: 16,
    paddingBottom: 32,
    borderTopWidth: 1,
    borderTopColor: "#1A1A1A",
  },
  actionContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    gap: 16,
  },
  completeButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
    gap: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    backgroundColor: "#6366F1",
  },
  completedButton: {
    backgroundColor: "#2E7D32",
    opacity: 0.8,
  },
  completeButtonText: {
    color: "#fff",
    fontFamily: "Inter",
    fontSize: 14,
    fontWeight: "500",
  },
  rightActions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 24,
  },
  tocLink: {
    padding: 4,
  },
  articleContainer: {
    backgroundColor: "#1A1A1A",
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  articleContent: {
    color: "#fff",
    fontFamily: "Inter",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 28,
  },
  processingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#1A1A1A",
  },
  processingTitle: {
    color: "#fff",
    fontFamily: "Inter",
    fontSize: 20,
    fontWeight: "600",
    marginTop: 16,
    marginBottom: 8,
  },
  processingSubtitle: {
    color: "#9A9A9A",
    fontFamily: "Inter",
    fontSize: 14,
    fontWeight: "400",
  },
  readMore: {
    color: "#6366F1",
    fontFamily: "Inter",
    fontSize: 12,
    fontWeight: "600",
    marginTop: 4,
  },
});

export default LessonScreen;
