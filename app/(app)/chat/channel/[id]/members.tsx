import { useLocalSearchParams } from "expo-router";
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  FlatList,
  Pressable,
} from "react-native";
import { Avatar } from "stream-chat-expo";
import { Channel as ChannelType } from "stream-chat";
import { Ionicons } from "@expo/vector-icons";
import { useAppContext } from "@/context/app";
import { router } from "expo-router";

interface ChannelData {
  name?: string;
  member_count?: number;
  image?: string;
}

export default function MembersPage() {
  const { id } = useLocalSearchParams();
  const { chatClient } = useAppContext();

  const channel = chatClient?.getChannelById("messaging", id as string, {}) as
    | (ChannelType & { data?: ChannelData })
    | undefined;

  if (!channel) {
    return (
      <SafeAreaView>
        <Text style={styles.errorText}>Channel not found</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="chevron-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Members</Text>
        </View>
      </View>
      <FlatList
        data={Object.values(channel.state.members)}
        keyExtractor={(member) => member.user?.id || ""}
        renderItem={({ item: member }) => (
          <Pressable
            style={({ pressed }) => [
              styles.memberItem,
              pressed && styles.memberItemPressed,
            ]}
            onPress={() => {
              if (member.user?.id) {
                router.push(`/profile/${member.user.id}`);
              }
            }}
          >
            <Avatar
              size={48}
              name={member.user?.name || member.user?.id}
              image={member.user?.image}
            />
            <View style={styles.memberInfo}>
              <Text style={styles.memberName}>
                {member.user?.name || member.user?.id}
              </Text>
              {member.user?.online && (
                <Text style={styles.onlineStatus}>Online</Text>
              )}
            </View>
            <Ionicons name="chevron-forward" size={20} color="#666" />
          </Pressable>
        )}
        contentContainerStyle={styles.listContent}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  header: {
    borderBottomWidth: 1,
    borderBottomColor: "#333",
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
  },
  backButton: {
    marginRight: 16,
    padding: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "800",
    color: "#fff",
  },
  listContent: {
    padding: 16,
  },
  memberItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  memberItemPressed: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
  },
  memberInfo: {
    marginLeft: 12,
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    color: "#fff",
    marginBottom: 4,
  },
  onlineStatus: {
    fontSize: 14,
    color: "#4CAF50",
  },
  errorText: {
    color: "#fff",
    fontSize: 16,
    textAlign: "center",
    marginTop: 20,
  },
});
