import {
  StyleSheet,
  View,
  SafeAreaView,
  TouchableOpacity,
  Text,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Channel, Thread } from "stream-chat-expo";
import { useLocalSearchParams, router } from "expo-router";
import { useAppContext } from "@/context/app";
import { CustomTypingIndicator } from "@/components/ui/TypingIndicator";

export default function ThreadScreen() {
  const { id } = useLocalSearchParams();
  const { thread, channel, setThread } = useAppContext();
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="chevron-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Threads</Text>
        </View>
      </View>
      <Channel
        channel={channel}
        thread={thread}
        threadList
        TypingIndicator={(props) => (
          <CustomTypingIndicator {...props} threadList={true} />
        )}
      >
        <View
          style={{
            flex: 1,
            justifyContent: "flex-start",
          }}
        >
          <Thread
            onThreadDismount={() => {
              setThread(undefined);
            }}
          />
        </View>
      </Channel>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  header: {
    borderBottomWidth: 1,
    borderBottomColor: "#333",
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
  },
  backButton: {
    marginRight: 16,
    padding: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "800",
    color: "#fff",
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: "flex-start",
  },
});
