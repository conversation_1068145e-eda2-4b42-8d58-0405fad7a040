import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  SafeAreaView,
  FlatList,
  Image,
} from "react-native";
import { router } from "expo-router";
import {
  Chat,
  OverlayProvider,
  SqliteClient,
  Streami18n,
  useCreateChatClient,
  ChannelList,
  Channel,
} from "stream-chat-expo";

import { useAppContext } from "@/context/app";

interface Chat {
  id: string;
  name: string;
  lastMessage: string;
  timestamp: string;
  avatar: string;
  unreadCount?: number;
}

const ChatsScreen: React.FC = () => {
  const { chatClient, userId } = useAppContext();

  const filters = {
    members: {
      $in: [userId],
    },
  };

  const sort = {
    last_message_at: -1,
  };

  const options = {
    presence: true,
    state: true,
    watch: true,
  };

  if (!chatClient) {
    return (
      <SafeAreaView style={styles.container}>
        <View>
          <Text>Loading ...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.inner}>
        <Text style={styles.title}>Messages</Text>
        <ChannelList
          filters={filters}
          sort={sort}
          options={options}
          onSelect={(channel) => {
            router.push(`/(app)/chat/channel/${channel.id}`);
          }}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  inner: {
    padding: 16,
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: "800",
    color: "#fff",
    marginBottom: 24,
  },
  chatItem: {
    flexDirection: "row",
    paddingVertical: 12,
    marginBottom: 4,
    borderRadius: 12,
    alignItems: "center",
    gap: 8,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  chatContent: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  name: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
  },
  timestamp: {
    fontSize: 12,
    color: "#9A9A9A",
  },
  messageRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  lastMessage: {
    fontSize: 12,
    color: "#9A9A9A",
    flex: 1,
    marginRight: 8,
  },
  unreadBadge: {
    backgroundColor: "#FF4545",
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 6,
  },
  unreadCount: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
  },
});

export default ChatsScreen;
