import React from "react";
import {
  StyleSheet,
  View,
  Text,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl,
  SectionList,
} from "react-native";
import { Bell } from "lucide-react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";

import { useNotifications, useReadNotification } from "@/lib/api/queries";
import { Notification } from "@/lib/api/types";
import { useAppContext } from "@/context/app";
import { NotificationItem } from "@/components/notifications";
import type {
  StreamNotificationGroup,
  UINotification,
} from "@/lib/stream/types";
import {
  transformStreamNotification,
  groupNotificationsByDate,
} from "@/lib/stream/transform";

interface ExtendedNotification extends Notification {
  type: "activity" | "announcement";
}

type CombinedNotification = ExtendedNotification | UINotification;

type TabType = "activities" | "announcements";

// Legacy notification item component for announcements
const LegacyNotificationItem: React.FC<{ item: ExtendedNotification }> = ({
  item,
}) => {
  const { mutate: markAsRead, isPending } = useReadNotification();

  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "Just now";
    if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} ${minutes === 1 ? "minute" : "minutes"} ago`;
    }
    if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} ${hours === 1 ? "hour" : "hours"} ago`;
    }
    if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} ${days === 1 ? "day" : "days"} ago`;
    }

    return new Date(dateString).toLocaleString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleNotificationClick = () => {
    if (!item.isRead && typeof item.id === "number") {
      markAsRead({ notificationId: item.id.toString() });
    }

    if (item.data?.link) {
      // Handle navigation for legacy notifications
      console.log("Legacy notification link:", item.data.link);
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.notificationItem,
        isPending && styles.notificationItemDisabled,
      ]}
      onPress={handleNotificationClick}
      disabled={isPending}
    >
      <View style={styles.notificationHeader}>
        <View style={[styles.avatar, styles.avatarPlaceholder]}>
          <MaterialIcons name="notifications" size={20} color="#fff" />
        </View>
        <View style={styles.notificationContent}>
          <Text style={styles.notificationTitle}>{item.title}</Text>
          <Text style={styles.notificationMessage}>{item.body}</Text>
          <Text style={styles.notificationTime}>
            {formatTimeAgo(item.createdAt)}
          </Text>
        </View>
        {!item.isRead && <View style={styles.unreadIndicator} />}
      </View>
    </TouchableOpacity>
  );
};

// Activities Tab Component (GetStream notifications)
const ActivitiesTab: React.FC = () => {
  const [streamNotifications, setStreamNotifications] = React.useState<
    UINotification[]
  >([]);
  const [streamLoading, setStreamLoading] = React.useState(false);
  const [streamHasNextPage, setStreamHasNextPage] = React.useState(false);
  const [streamIsFetchingNextPage, setStreamIsFetchingNextPage] =
    React.useState(false);
  const [streamNextPageParam, setStreamNextPageParam] = React.useState<
    string | undefined
  >(undefined);
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const { userId, streamClient } = useAppContext();

  const fetchStreamNotifications = React.useCallback(
    async (markSeen: boolean = false, isLoadMore: boolean = false) => {
      if (!streamClient) {
        setStreamNotifications([]);
        setStreamHasNextPage(false);
        setStreamNextPageParam(undefined);
        return;
      }

      if (isLoadMore) {
        setStreamIsFetchingNextPage(true);
      } else {
        setStreamLoading(true);
      }

      try {
        const notificationFeed = streamClient.feed("notification", userId);
        const queryParams: any = {
          limit: 20,
          withReactionCounts: true,
          withOwnReactions: true,
          mark_seen: markSeen,
        };

        if (isLoadMore && streamNextPageParam) {
          queryParams.id_lt = streamNextPageParam;
        }

        const response = await notificationFeed.get(queryParams);
        console.log("response", response);

        // Debug logging
        console.log("Stream API Response:", JSON.stringify(response, null, 2));

        if (!response || !response.results) {
          console.warn("Invalid response from Stream API");
          throw new Error("Invalid response structure");
        }

        const streamGroups = response.results as StreamNotificationGroup[];
        console.log("Stream Groups:", streamGroups.length, "groups found");

        if (streamGroups.length > 0) {
          console.log(
            "First group sample:",
            JSON.stringify(streamGroups[0], null, 2)
          );
        }

        const transformedNotifications = streamGroups
          .map((group, index) => {
            try {
              return transformStreamNotification(group);
            } catch (error) {
              console.error(`Error transforming group ${index}:`, group, error);
              return null;
            }
          })
          .filter(Boolean) as UINotification[];

        if (isLoadMore) {
          // Append to existing notifications
          setStreamNotifications((prev) => [
            ...prev,
            ...transformedNotifications,
          ]);
        } else {
          // Replace notifications (initial load or refresh)
          setStreamNotifications(transformedNotifications);
        }

        // Update pagination state
        const hasMore = streamGroups.length >= 20;
        setStreamHasNextPage(hasMore);

        if (streamGroups.length > 0) {
          const lastNotification = streamGroups[streamGroups.length - 1];
          setStreamNextPageParam(lastNotification.id);
        } else {
          setStreamNextPageParam(undefined);
        }

        // If we marked notifications as seen, update the counts
        if (markSeen && !isLoadMore) {
          // TODO: Update notification counts in context
          console.log("Notifications marked as seen");
        }
      } catch (error) {
        console.error("Error fetching Stream notifications:", error);
        if (!isLoadMore) {
          setStreamNotifications([]);
          setStreamHasNextPage(false);
          setStreamNextPageParam(undefined);
        }
      } finally {
        if (isLoadMore) {
          setStreamIsFetchingNextPage(false);
        } else {
          setStreamLoading(false);
        }
      }
    },
    [streamClient, userId, streamNextPageParam]
  );

  // Mark notifications as seen when the page loads
  React.useEffect(() => {
    if (streamClient && userId) {
      fetchStreamNotifications(true);
    }
  }, [streamClient, userId]);

  // Function to mark specific notifications as read
  const markNotificationAsRead = React.useCallback(
    async (notificationId: string) => {
      if (!streamClient) {
        console.error("Stream client not available");
        return;
      }

      try {
        const notificationFeed = streamClient.feed("notification", userId);
        await notificationFeed.get({
          limit: 1,
          mark_read: [notificationId],
        });

        // Update local state to reflect the read status
        setStreamNotifications((prev) =>
          prev.map((notification) =>
            notification.id === notificationId
              ? { ...notification, isRead: true }
              : notification
          )
        );

        // Update notification counts
        const currentNotification = streamNotifications.find(
          (n) => n.id === notificationId
        );

        if (currentNotification && !currentNotification.isRead) {
          // TODO: Update notification counts
          console.log("Notification marked as read:", notificationId);
        }
      } catch (error) {
        console.error("Error marking notification as read:", error);
      }
    },
    [streamClient, userId, streamNotifications]
  );

  // Function to load more stream notifications
  const loadMoreStreamNotifications = React.useCallback(() => {
    if (streamHasNextPage && !streamIsFetchingNextPage) {
      fetchStreamNotifications(false, true);
    }
  }, [streamHasNextPage, streamIsFetchingNextPage, fetchStreamNotifications]);

  const handleRefresh = React.useCallback(async () => {
    setIsRefreshing(true);
    try {
      // Reset pagination state before refreshing
      setStreamNextPageParam(undefined);
      setStreamHasNextPage(false);
      await fetchStreamNotifications(true);
    } catch (error) {
      console.error("Error refreshing notifications:", error);
    } finally {
      setIsRefreshing(false);
    }
  }, [fetchStreamNotifications]);

  // Group notifications by date
  const groupedNotifications = React.useMemo(() => {
    return groupNotificationsByDate(streamNotifications);
  }, [streamNotifications]);

  const renderSectionHeader = ({ section }: { section: { title: string } }) => (
    <Text style={styles.sectionHeader}>{section.title}</Text>
  );

  const renderFooter = () => {
    if (!streamIsFetchingNextPage) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color="#007AFF" />
        <Text style={styles.footerText}>Loading more activities...</Text>
      </View>
    );
  };

  const renderEmptyState = () => {
    if (streamLoading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color="#fff" />
          <Text style={styles.emptyMessage}>Loading activities...</Text>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Bell size={48} color="#838A94" />
        <Text style={styles.emptyTitle}>No Activities Yet</Text>
        <Text style={styles.emptyMessage}>
          We'll notify you when there's activity to see
        </Text>
      </View>
    );
  };

  // Convert grouped notifications to sections format
  const sections = Object.entries(groupedNotifications).map(
    ([dateLabel, notifications]) => ({
      title: dateLabel,
      data: notifications,
    })
  );

  return (
    <>
      {streamNotifications.length > 0 ? (
        <SectionList
          sections={sections}
          renderItem={({ item }) => (
            <NotificationItem
              notification={item}
              onMarkRead={markNotificationAsRead}
            />
          )}
          renderSectionHeader={renderSectionHeader}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={10}
          onEndReached={loadMoreStreamNotifications}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={["#007AFF"]}
              tintColor="#007AFF"
              title="Refreshing..."
              titleColor="#9A9A9A"
              progressBackgroundColor="#1A1A1A"
            />
          }
        />
      ) : (
        renderEmptyState()
      )}
    </>
  );
};

// Announcements Tab Component (Sphere notifications)
const AnnouncementsTab: React.FC = () => {
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const {
    data,
    isLoading,
    isError,
    refetch,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useNotifications();

  // Determine notification type based on content or other criteria
  const determineNotificationType = (): "activity" | "announcement" => {
    // Sphere notifications from useNotifications() should go to "Announcements" tab
    return "announcement";
  };

  // Filter notifications for announcements
  const filteredNotifications = React.useMemo(() => {
    // Flatten all pages from infinite query into a single array
    const regularNotifications: ExtendedNotification[] =
      data?.pages?.flatMap((page) =>
        page.success
          ? page.data.notifications.map((notification: any) => ({
              ...notification,
              type: determineNotificationType(),
            }))
          : []
      ) || [];

    // Sort by creation date (newest first)
    regularNotifications.sort((a, b) => {
      const dateA = new Date(a.createdAt);
      const dateB = new Date(b.createdAt);
      return dateB.getTime() - dateA.getTime();
    });

    return regularNotifications;
  }, [data?.pages]);

  const renderNotification = ({ item }: { item: ExtendedNotification }) => {
    return <LegacyNotificationItem item={item} />;
  };

  const onEndReached = React.useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      console.log("Announcements: Loading more notifications...");
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const renderFooter = () => {
    if (!isFetchingNextPage) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.footerText}>Loading more announcements...</Text>
      </View>
    );
  };

  const renderEmptyState = () => {
    if (isLoading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color="#fff" />
          <Text style={styles.emptyMessage}>Loading announcements...</Text>
        </View>
      );
    }

    if (isError) {
      return (
        <View style={styles.emptyContainer}>
          <MaterialIcons name="error-outline" size={48} color="#838A94" />
          <Text style={styles.emptyTitle}>Something went wrong</Text>
          <Text style={styles.emptyMessage}>
            We couldn't load your announcements
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Bell size={48} color="#838A94" />
        <Text style={styles.emptyTitle}>No Announcements Yet</Text>
        <Text style={styles.emptyMessage}>
          We'll notify you as soon as we hear anything
        </Text>
      </View>
    );
  };

  return (
    <>
      {filteredNotifications.length > 0 ? (
        <FlatList
          data={filteredNotifications}
          renderItem={renderNotification}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={10}
          onEndReached={onEndReached}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={async () => {
                setIsRefreshing(true);
                try {
                  await refetch();
                } catch (error) {
                  console.error("Failed to refresh announcements:", error);
                  Alert.alert(
                    "Error",
                    "Failed to refresh announcements. Please try again."
                  );
                } finally {
                  setIsRefreshing(false);
                }
              }}
              colors={["#007AFF"]}
              tintColor="#007AFF"
              title="Refreshing..."
              titleColor="#9A9A9A"
              progressBackgroundColor="#1A1A1A"
            />
          }
          getItemLayout={(_, index) => ({
            length: 100, // Approximate height of each item
            offset: 100 * index,
            index,
          })}
        />
      ) : (
        renderEmptyState()
      )}
    </>
  );
};

const NotificationsScreen: React.FC = () => {
  const [activeTab, setActiveTab] = React.useState<TabType>("activities");

  const handleTabPress = (tab: TabType) => {
    setActiveTab(tab);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.inner}>
        <Text style={styles.title}>Notifications</Text>

        {/* Tabs */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === "activities" && styles.activeTab]}
            onPress={() => handleTabPress("activities")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "activities" && styles.activeTabText,
              ]}
            >
              Activities
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.tab,
              activeTab === "announcements" && styles.activeTab,
            ]}
            onPress={() => handleTabPress("announcements")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "announcements" && styles.activeTabText,
              ]}
            >
              Announcements
            </Text>
          </TouchableOpacity>
        </View>

        {/* Render the appropriate tab component */}
        {activeTab === "activities" ? <ActivitiesTab /> : <AnnouncementsTab />}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  inner: {
    padding: 16,
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: "800",
    color: "#fff",
    marginBottom: 24,
  },
  tabContainer: {
    flexDirection: "row",
    gap: 8,
    marginBottom: 16,
  },
  tab: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 12,
    backgroundColor: "#585858",
  },
  activeTab: {
    backgroundColor: "#fff",
  },
  tabText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#B0B0B0",
    lineHeight: 16,
  },
  activeTabText: {
    color: "#242424",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "#fff",
    textAlign: "center",
  },
  emptyMessage: {
    fontSize: 16,
    color: "#9A9A9A",
    textAlign: "center",
  },
  listContent: {
    paddingBottom: 16,
  },
  notificationItem: {
    backgroundColor: "#1A1A1A",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  notificationItemDisabled: {
    opacity: 0.7,
  },
  notificationHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  avatarPlaceholder: {
    backgroundColor: "#585858",
    justifyContent: "center",
    alignItems: "center",
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 4,
  },
  notificationMessage: {
    fontSize: 14,
    color: "#9A9A9A",
    marginBottom: 8,
  },
  notificationTime: {
    fontSize: 12,
    color: "#585858",
  },
  unreadIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#007AFF",
    marginLeft: 8,
  },
  footerLoader: {
    paddingVertical: 20,
    alignItems: "center",
    justifyContent: "center",
  },
  footerText: {
    color: "#9A9A9A",
    fontSize: 14,
    marginTop: 8,
    opacity: 0.7,
  },
  sectionHeader: {
    fontSize: 12,
    fontWeight: "600",
    color: "#585858",
    textTransform: "uppercase",
    marginBottom: 8,
    marginTop: 16,
  },
});

export default NotificationsScreen;
