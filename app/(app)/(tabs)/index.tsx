import React, { useState, useMemo, useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { Link, router } from "expo-router";
import { ArrowRightLeft } from "lucide-react-native";
import { Image } from "expo-image";

import { Course } from "@/components/modules/group/Course";
import { Info } from "@/components/modules/group/Info";
import { useGroup, useCohort } from "@/lib/api/queries";
import { useAppContext } from "@/context/app";
import type { Module } from "@/lib/api/types";
import { Links } from "@/components/modules/group/Links";
import { Events } from "@/components/modules/group/Events";
import { Chat } from "@/components/modules/group/Chat";
import { Feed } from "@/components/modules/group/Feed";

const HomeScreen: React.FC = () => {
  const [activeTab, setActiveTab] = useState<number | "info" | "chat">();
  const [activeTabModule, setActiveTabModule] = useState<
    Module | "info" | "chat"
  >();
  const { groupId, cohortId } = useAppContext();

  const { data: groupData } = useGroup(groupId);

  const { data: cohortData } = useCohort(groupId, cohortId);

  const handleExplore = () => {
    router.push("/explore");
  };

  const cohort = useMemo(() => {
    if (!cohortData?.data) return null;

    const sortedCohort = {
      ...cohortData.data,
      modules: [...cohortData.data.modules].sort((a, b) => a.order - b.order),
    };

    return sortedCohort;
  }, [cohortData?.data]);

  const hasDiscussionModules = useMemo(() => {
    return (
      cohort?.modules.some((module) => module.type === "discussion") ?? false
    );
  }, [cohort?.modules]);

  const discussionModules = useMemo(() => {
    return (
      cohort?.modules.filter((module) => module.type === "discussion") ?? []
    );
  }, [cohort?.modules]);

  useEffect(() => {
    if (cohort) {
      const availableModules = cohort.modules.filter(
        (module) => module.type !== "discussion"
      );

      if (availableModules.length == 0) {
        if (hasDiscussionModules) {
          changeActiveTab("chat");
        } else {
          changeActiveTab("info");
        }
      } else {
        changeActiveTab(availableModules[0]);
      }
    }
  }, [cohort]);

  if (!groupId || !cohortId) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.emptyStateContainer}>
          <View style={styles.emptyStateImagesContainer}>
            <View style={styles.emptyStateImageWrapper}>
              <Image
                source={require("@/assets/images/empty1.jpg")}
                style={styles.emptyStateImage}
                contentFit="cover"
              />
              <View style={styles.emptyStateImageOverlay} />
            </View>
            <View style={styles.emptyStateImageWrapper}>
              <Image
                source={require("@/assets/images/empty2.jpg")}
                style={styles.emptyStateImage}
                contentFit="cover"
              />
              <View style={styles.emptyStateImageOverlay} />
            </View>
            <View style={styles.emptyStateImageWrapper}>
              <View style={styles.emptyStatePlaceholderImage} />
              <View style={styles.emptyStateImageOverlay} />
            </View>
          </View>

          <Text style={styles.emptyStateTitle}>No group joined... yet!</Text>
          <Text style={styles.emptyStateDescription}>
            Join the new group and connect with like-minded people and join
            wholesome events
          </Text>

          <TouchableOpacity
            style={styles.emptyStateButton}
            onPress={handleExplore}
          >
            <Text style={styles.emptyStateButtonText}>Explore Group</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const changeActiveTab = (module: Module | "info" | "chat") => {
    if (module === "info") {
      setActiveTab("info");
      setActiveTabModule("info");
    } else if (module === "chat") {
      setActiveTab("chat");
      setActiveTabModule("chat");
    } else {
      setActiveTab(module.id);
      setActiveTabModule(module);
    }
  };

  const renderContent = () => {
    return (
      <View>
        <View style={styles.header}>
          <View style={styles.groupInfo}>
            <Link href="/group-selection" style={styles.cohortToggle}>
              <ArrowRightLeft color="#fff" size={20} />
            </Link>
            <View style={styles.groupHeader}>
              <Image
                source={{ uri: groupData?.data.bannerImage }}
                style={styles.groupAvatar}
              />
              <View style={styles.groupTextContainer}>
                <Text style={styles.groupName}>{groupData?.data.name}</Text>
                <Text style={styles.cohortName}>{cohort?.name}</Text>
              </View>
            </View>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.tabsContainer}
          >
            <View style={styles.tabs}>
              {cohort?.modules
                ?.filter((module) => module.type !== "discussion")
                .map((module) => (
                  <TouchableOpacity
                    key={module.id}
                    onPress={() => changeActiveTab(module)}
                  >
                    <Text
                      style={[
                        styles.tab,
                        activeTab === module.id && styles.activeTab,
                      ]}
                    >
                      {module.name}
                    </Text>
                    {activeTab === module.id && (
                      <View style={styles.tabIndicator} />
                    )}
                  </TouchableOpacity>
                ))}
              {hasDiscussionModules && (
                <TouchableOpacity onPress={() => changeActiveTab("chat")}>
                  <Text
                    style={[
                      styles.tab,
                      activeTab === "chat" && styles.activeTab,
                    ]}
                  >
                    Chat
                  </Text>
                  {activeTab === "chat" && <View style={styles.tabIndicator} />}
                </TouchableOpacity>
              )}
              <TouchableOpacity onPress={() => changeActiveTab("info")}>
                <Text
                  style={[styles.tab, activeTab === "info" && styles.activeTab]}
                >
                  Info
                </Text>
                {activeTab === "info" && <View style={styles.tabIndicator} />}
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>

        <View style={styles.scrollViewContainer}>
          {/* We don't unmount the info tab to prevent the slow loading of the webview for the subsequence tab switch */}
          <View
            style={{ display: activeTabModule === "info" ? "flex" : "none" }}
          >
            <Info groupId={groupId!} bio={cohort?.bio || null} />
          </View>
          {activeTabModule !== "info" && activeTabModule !== "chat" && (
            <>
              {activeTabModule?.type === "course" && (
                <View style={{ paddingHorizontal: 16 }}>
                  <Course courseId={activeTabModule.config.courseId} />
                </View>
              )}
              {activeTabModule?.type === "links" && (
                <View style={{ paddingHorizontal: 16 }}>
                  <Links
                    module={activeTabModule}
                    groupId={groupId!}
                    cohortId={cohortId!}
                  />
                </View>
              )}
              {activeTabModule?.type === "events" && (
                <View style={{ paddingHorizontal: 16 }}>
                  <Events
                    groupId={groupId!}
                    cohortId={cohortId!}
                    moduleId={activeTabModule.id}
                  />
                </View>
              )}
              {activeTabModule?.type === "feed" && (
                <View style={{ paddingHorizontal: 16 }}>
                  <Feed module={activeTabModule} />
                </View>
              )}
            </>
          )}
          {activeTab === "chat" && (
            <View style={{ paddingHorizontal: 16 }}>
              <Chat discussionModules={discussionModules} />
            </View>
          )}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>{renderContent()}</SafeAreaView>
  );
};

export default HomeScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  emptyStateContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 24,
    alignItems: "center",
    justifyContent: "center",
  },
  emptyStateImagesContainer: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 32,
    paddingHorizontal: 16,
  },
  emptyStateImageWrapper: {
    width: 110,
    height: 160,
    borderRadius: 10,
    overflow: "hidden",
    position: "relative",
  },
  emptyStateImage: {
    width: "100%",
    height: "100%",
  },
  emptyStateImageOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0, 0, 0, 0.4)",
  },
  emptyStatePlaceholderImage: {
    width: "100%",
    height: "100%",
    backgroundColor: "#333333",
  },
  emptyStateTitle: {
    color: "#FFFFFF",
    fontFamily: "Inter",
    fontSize: 24,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: 16,
  },
  emptyStateDescription: {
    color: "#9A9A9A",
    fontFamily: "Inter",
    fontSize: 16,
    textAlign: "center",
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  emptyStateButton: {
    backgroundColor: "#6366F1",
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 4,
  },
  emptyStateButtonText: {
    color: "#FFFFFF",
    fontFamily: "Inter",
    fontSize: 12,
    fontWeight: "500",
  },
  emptyStateTabBar: {
    flexDirection: "row",
    backgroundColor: "#131313",
    paddingVertical: 16,
    paddingHorizontal: 24,
    justifyContent: "space-between",
  },
  emptyStateTabItem: {
    alignItems: "center",
    gap: 5,
    width: 50,
  },
  emptyStateTabText: {
    color: "#FFFFFF",
    fontFamily: "Inter",
    fontSize: 10,
  },
  emptyStateActiveTabText: {
    color: "#EF5252",
  },

  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: "#000",
    paddingTop: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#242424",
  },
  headerTop: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  menuButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: "rgba(38, 38, 38, 0.5)",
  },
  groupInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 14,
    gap: 8,
  },
  groupHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  groupAvatar: {
    width: 40,
    height: 40,
    borderRadius: 4,
  },
  groupTextContainer: {
    flex: 1,
    justifyContent: "center",
    gap: 3,
  },
  groupName: {
    fontSize: 16,
    fontWeight: "700",
    color: "#fff",
  },
  cohortName: {
    fontSize: 12,
    color: "#FFFFFF",
    opacity: 0.5,
  },
  groupBadge: {
    backgroundColor: "#262626",
    paddingVertical: 6,
    paddingHorizontal: 6,
    borderRadius: 4,
  },
  groupBadgeText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#fff",
  },
  tabsContainer: {
    paddingHorizontal: 12,
  },
  tabs: {
    flexDirection: "row",
    gap: 20,
    paddingBottom: 12,
    transform: [{ translateY: 13 }],
  },
  tab: {
    fontSize: 14,
    fontWeight: "500",
    color: "#838A94",
    paddingBottom: 10,
    position: "relative",
  },
  activeTab: {
    color: "#EF5252",
  },
  emptyTab: {
    flex: 1,
    backgroundColor: "#000",
  },
  tabIndicator: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 2,
    backgroundColor: "#EF5252",
    borderRadius: 2,
  },
  progressCard: {
    margin: 16,
    padding: 8,
    backgroundColor: "rgba(53, 53, 53, 0.55)",
    borderRadius: 8,
  },
  progressTitle: {
    fontSize: 10,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 10,
  },
  progressInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
    marginBottom: 10,
  },
  progressChapter: {
    fontSize: 12,
    fontWeight: "600",
    color: "rgba(255, 255, 255, 0.5)",
    width: 240,
  },
  progressPercentage: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
  },
  progressBar: {
    height: 3,
    backgroundColor: "rgba(217, 217, 217, 0.12)",
    borderRadius: 10,
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#EF5252",
    borderRadius: 10,
  },
  feedCard: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    backgroundColor: "#171D23",
    borderRadius: 8,
  },
  feedHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  avatar: {
    width: 38,
    height: 38,
    borderRadius: 19,
  },
  userMeta: {
    gap: 2,
  },
  nameContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  name: {
    fontSize: 14,
    fontWeight: "700",
    color: "#fff",
  },
  creatorBadge: {
    backgroundColor: "#EF5252",
    paddingVertical: 2,
    paddingHorizontal: 4,
    borderRadius: 4,
  },
  creatorText: {
    fontSize: 8,
    fontWeight: "500",
    color: "#fff",
  },
  timestamp: {
    fontSize: 12,
    color: "#9A9A9A",
  },
  feedContent: {
    gap: 4,
    marginBottom: 16,
  },
  feedTitle: {
    fontSize: 14,
    fontWeight: "700",
    color: "#fff",
  },
  feedText: {
    fontSize: 14,
    color: "#D9D9D9",
  },
  feedActions: {
    flexDirection: "row",
    gap: 20,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  actionText: {
    fontSize: 14,
    fontWeight: "700",
    color: "rgba(255, 255, 255, 0.8)",
  },
  cohortToggle: {
    padding: 8,
  },
  scrollViewContainer: {
    paddingVertical: 12,
    marginBottom: 200,
  },
  discussionModuleContainer: {
    marginBottom: 24,
  },
  discussionModuleTitle: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
});
