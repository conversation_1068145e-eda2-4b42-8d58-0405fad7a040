import { View, Text, StyleSheet, Image, Pressable } from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import { MaterialIcons, Feather } from "@expo/vector-icons";

import { useAppContext } from "@/context/app";
import { useProfileById } from "@/lib/api/queries";

const UserProfileSheet = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { chatClient, userId } = useAppContext();
  const { data: profileData, isLoading } = useProfileById(id);

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  const userData = profileData?.data;
  const isCreator = userData?.role === "creator";
  const isOwnProfile = userId === id;

  return (
    <View style={styles.container}>
      {/* Header with close button */}
      <View style={styles.header}>
        <Pressable onPress={() => router.back()} style={styles.closeButton}>
          <MaterialIcons name="close" size={24} color="#fff" />
        </Pressable>
      </View>

      {/* Profile Content */}
      <View style={styles.content}>
        {/* Profile Image */}
        <Image
          source={{ uri: userData?.avatarUrl || "https://picsum.photos/200" }}
          style={styles.profileImage}
        />

        {/* Name and Role */}
        <View style={styles.nameContainer}>
          <Text style={styles.name}>
            {userData?.firstName} {userData?.lastName}
          </Text>
          {isCreator && (
            <View style={styles.roleTag}>
              <Text style={styles.roleTagText}>Creator</Text>
            </View>
          )}
          <Text style={styles.email}>{userData?.email}</Text>
        </View>

        {/* Action Buttons */}
        {!isOwnProfile && (
          <View style={styles.actionButtons}>
            <Pressable
              style={[styles.button, styles.chatButton]}
              onPress={async () => {
                try {
                  const channel = chatClient?.channel("messaging", {
                    members: [userId as string, id],
                  });

                  if (!channel) {
                    console.error("Chat client or channel not initialized");
                    return;
                  }

                  await channel.create();

                  // Redirect to chat screen
                  router.back();
                  router.push(`/(app)/(tabs)/chats`);
                  router.push(`/(app)/chat/channel/${channel.id}`);
                } catch (error) {
                  console.error("Error creating channel:", error);
                }
              }}
            >
              <Feather name="message-circle" size={20} color="#fff" />
              <Text style={styles.buttonText}>Message</Text>
            </Pressable>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1A1A1A",
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 16,
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  closeButton: {
    padding: 8,
  },
  content: {
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 24,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 16,
  },
  nameContainer: {
    alignItems: "center",
    marginBottom: 24,
  },
  name: {
    fontSize: 24,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 8,
  },
  email: {
    fontSize: 14,
    color: "#9CA3AF",
    marginTop: 4,
  },
  roleTag: {
    backgroundColor: "#6366F1",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  roleTagText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "500",
  },
  actionButtons: {
    flexDirection: "row",
    gap: 12,
  },
  button: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  chatButton: {
    backgroundColor: "#2A2A2A",
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
  },
  loadingText: {
    color: "#fff",
    fontSize: 16,
    textAlign: "center",
    marginTop: 24,
  },
});

export default UserProfileSheet;
