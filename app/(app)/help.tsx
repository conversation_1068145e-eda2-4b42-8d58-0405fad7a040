import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { Stack, router } from "expo-router";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

interface FAQItem {
  question: string;
  answer: string;
  isExpanded: boolean;
}

const HelpScreen: React.FC = () => {
  const [faqs, setFaqs] = useState<FAQItem[]>([
    {
      question: "How do I join a group?",
      answer:
        'To join a group, navigate to the Explore tab and search for groups that interest you. Once you find a group, tap on it and press the "Join" button. Some groups may require approval from administrators.',
      isExpanded: false,
    },
    {
      question: "How do I create a new post?",
      answer:
        'To create a new post, go to the group feed and tap on the "+" button at the bottom of the screen. You can then add text, images, or videos to your post before publishing it.',
      isExpanded: false,
    },
    {
      question: "How do I change my notification settings?",
      answer:
        "To change your notification settings, go to Settings > Notifications. From there, you can customize which notifications you receive and how you receive them.",
      isExpanded: false,
    },
    {
      question: "How do I update my profile information?",
      answer:
        'To update your profile information, go to your Profile tab and tap on the "Edit Profile" button. You can then update your name, bio, profile picture, and other information.',
      isExpanded: false,
    },
    {
      question: "How do I report inappropriate content?",
      answer:
        'To report inappropriate content, tap the three dots (...) on the post or comment and select "Report". You will be asked to provide a reason for the report, which will be reviewed by our moderation team.',
      isExpanded: false,
    },
  ]);

  const toggleFAQ = (index: number) => {
    const updatedFaqs = [...faqs];
    updatedFaqs[index].isExpanded = !updatedFaqs[index].isExpanded;
    setFaqs(updatedFaqs);
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: "Help Center",
          headerStyle: {
            backgroundColor: "#121212",
          },
          headerTintColor: "#fff",
          headerTitleStyle: {
            fontWeight: "bold",
          },
          headerLeft: () => (
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <MaterialIcons name="arrow-back" size={24} color="#fff" />
            </TouchableOpacity>
          ),
        }}
      />

      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>How can we help you?</Text>
          <Text style={styles.headerSubtitle}>
            Find answers to common questions or contact our support team.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>

          {faqs.map((faq, index) => (
            <View key={index} style={styles.faqItem}>
              <TouchableOpacity
                style={styles.faqQuestion}
                onPress={() => toggleFAQ(index)}
              >
                <Text style={styles.faqQuestionText}>{faq.question}</Text>
                <MaterialIcons
                  name={
                    faq.isExpanded ? "keyboard-arrow-up" : "keyboard-arrow-down"
                  }
                  size={24}
                  color="#FF4545"
                />
              </TouchableOpacity>

              {faq.isExpanded && (
                <Text style={styles.faqAnswer}>{faq.answer}</Text>
              )}
            </View>
          ))}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contact Support</Text>

          <TouchableOpacity style={styles.contactItem}>
            <View style={styles.contactInfo}>
              <MaterialIcons name="email" size={24} color="#fff" />
              <Text style={styles.contactText}>Email Support</Text>
            </View>
            <MaterialIcons name="chevron-right" size={24} color="#666" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.contactItem}>
            <View style={styles.contactInfo}>
              <MaterialIcons name="chat" size={24} color="#fff" />
              <Text style={styles.contactText}>Live Chat</Text>
            </View>
            <MaterialIcons name="chevron-right" size={24} color="#666" />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  backButton: {
    marginLeft: 10,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 24,
    backgroundColor: "#121212",
    borderBottomWidth: 1,
    borderBottomColor: "#333",
  },
  headerTitle: {
    color: "#fff",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
  },
  headerSubtitle: {
    color: "#aaa",
    fontSize: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    color: "#FF4545",
    fontSize: 18,
    fontWeight: "bold",
    marginHorizontal: 16,
    marginBottom: 16,
    marginTop: 24,
  },
  faqItem: {
    backgroundColor: "#121212",
    marginBottom: 8,
    borderRadius: 8,
    overflow: "hidden",
    marginHorizontal: 16,
  },
  faqQuestion: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
  },
  faqQuestionText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
    flex: 1,
  },
  faqAnswer: {
    color: "#aaa",
    fontSize: 14,
    lineHeight: 20,
    padding: 16,
    paddingTop: 0,
  },
  contactItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    backgroundColor: "#121212",
    marginBottom: 8,
    borderRadius: 8,
    marginHorizontal: 16,
  },
  contactInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  contactText: {
    color: "#fff",
    fontSize: 16,
    marginLeft: 16,
  },
});

export default HelpScreen;
