import { useEffect } from "react";
import { router } from "expo-router";
import SuperTokens from "supertokens-react-native";

export default function Index() {
  useEffect(() => {
    SuperTokens.doesSessionExist().then((exists) => {
      if (!exists) {
        console.log("No session exists");
        router.replace("/sign-in");
      } else {
        console.log("Session exists");
        router.replace("/(app)/(tabs)");
      }
    });
  }, []);
}
