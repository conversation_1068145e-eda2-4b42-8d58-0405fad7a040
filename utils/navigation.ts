import { router } from "expo-router";

// Helper functions to navigate between screens
export const navigateTo = {
  // Auth screens
  signIn: () => {
    // Using a more type-safe approach by casting the string
    router.replace("sign-in" as any);
  },
  signUp: () => {
    router.replace("sign-up" as any);
  },

  // Main app screens
  home: () => {
    router.replace("/(tabs)" as any);
  },

  // Other screens
  settings: () => {
    router.push("settings" as any);
  },
  help: () => {
    router.push("help" as any);
  },
  groupSelection: () => {
    router.push("group-selection" as any);
  },
};
