# AGENT.md - Sphere App Development Guide

## Commands

- **Start dev server**: `npm start` or `npx expo start`
- **Platform-specific**: `npm run android`, `npm run ios`, `npm run web`
- **Test**: `npm run test` (jest --watchAll), single test: `npx jest path/to/test.test.ts`
- **Lint**: `npm run lint` (ESLint with Expo preset)
- **Build**: Use EAS Build for production builds

## Architecture

- **React Native + Expo Router** with file-based routing in `app/` directory
- **Tab navigation**: chats, explore, index (home), notifications, profile
- **Authentication**: SuperTokens with session management
- **State management**: TanStack React Query + AppProvider context (`context/app.tsx`)
- **Chat**: Stream Chat integration with group/cohort channels
- **Storage**: AsyncStorage for local persistence, Firebase for push notifications

## Code Style (from .windsurfrules)

- **TypeScript**: Strict mode, use interfaces over types, avoid `any`, use `React.FC`
- **Naming**: camelCase for variables/functions, PascalCase for components, lowercase-hyphenated directories
- **Imports**: Use `@/*` path alias for root imports (e.g., `@/context/app`, `@/components/ui`)
- **Icons**: Use `@expo/vector-icons/MaterialIcons`
- **Package manager**: Use `npm` (not yarn/pnpm)

## Error Handling & Performance

- Use try-catch utility from `utils/try-catch.ts`
- Optimize FlatLists with `removeClippedSubviews`, `maxToRenderPerBatch`, avoid anonymous functions
- Use `React.memo()` for static components, minimize `useEffect` in render methods
- API responses follow `{success: boolean, data: T}` structure
