{"name": "sphere-app", "main": "expo-router/entry", "version": "0.1.5", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.4", "@lodev09/react-native-true-sheet": "^2.0.5", "@react-native-async-storage/async-storage": "1.24.0", "@react-native-community/netinfo": "11.4.1", "@react-native-firebase/app": "^22.1.0", "@react-native-firebase/messaging": "^22.1.0", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/bottom-tabs": "^7.3.12", "@react-navigation/native": "^7.1.8", "@tanstack/react-query": "^5.77.0", "expo": "~52.0.41", "expo-blur": "~14.0.3", "expo-build-properties": "~0.13.2", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.15", "expo-device": "~7.0.3", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image": "~2.0.7", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "^14.0.2", "expo-linking": "~7.0.5", "expo-notifications": "~0.29.14", "expo-router": "~4.0.19", "expo-secure-store": "~14.0.1", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.8", "expo-video": "~2.0.6", "expo-web-browser": "~14.0.2", "getstream": "^8.8.0", "lucide-react-native": "^0.485.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "~0.77.1", "react-native-gesture-handler": "~2.25.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "~5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-web": "~0.19.13", "react-native-webview": "13.13.5", "stream-chat-expo": "^7.0.0", "supertokens-react-native": "^5.1.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@tanstack/eslint-plugin-query": "^5.66.1", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.3", "react-test-renderer": "18.3.1", "typescript": "^5.8.3"}, "expo": {"install": {"exclude": ["react-native@~0.76.6", "react-native-reanimated@~3.16.1", "react-native-gesture-handler@~2.20.0", "react-native-screens@~4.4.0", "react-native-safe-area-context@~4.12.0", "react-native-webview@~13.12.5"]}}, "private": true}