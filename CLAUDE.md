# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Sphere is a React Native learning platform app built with Expo Router. The app features group-based learning, cohorts, live classes, courses with lessons, and integrated chat functionality. Users can join groups, participate in cohorts, take courses, and communicate through Stream Chat.

## Key Technologies

- **Expo Router** with file-based routing
- **SuperTokens** for authentication
- **Stream Chat** for messaging
- **TanStack React Query** for state management and API calls
- **AsyncStorage** for local data persistence
- **React Native Firebase** for push notifications
- **Google Sign-In** integration

## Development Commands

```bash
# Start the development server
npm start
# or
npx expo start

# Run on specific platforms
npm run android
npm run ios
npm run web

# Run tests (Jest with watch mode)
npm run test

# Lint code (ESLint with Expo preset)
npm run lint

# Reset project (moves starter code to app-example)
npm run reset-project
```

## Environment Setup

- Requires `.env` file with `EXPO_PUBLIC_API_DOMAIN` and `EXPO_PUBLIC_STREAM_API_KEY`
- Firebase configuration files: `GoogleService-Info.plist` (iOS) and `google-services.json` (Android)
- Uses TypeScript with strict mode enabled and path aliases (`@/*` → `./`)
- ESLint configuration extends `expo` preset with `/dist/*` ignored

## Architecture

### App Structure

- **File-based routing** using Expo Router in the `app/` directory
- **Tab-based navigation** with main tabs: chats, explore, index (home), notifications, profile
- **Nested routes** for groups, cohorts, courses, and live classes
- **Authentication flow** with sign-in/sign-up pages and protected routes

### State Management

- **AppProvider** (`context/app.tsx`) manages global state including authentication, user ID, group/cohort selection, and Stream Chat client
- **React Query** for server state management with normalized data structures
- **AsyncStorage** for persisting group/cohort selections across app sessions

### API Integration

- All API calls centralized in `lib/api/queries.ts` using React Query hooks
- API responses follow a consistent structure with `success` flag and typed data
- Optimistic updates implemented for critical mutations (profile editing, lesson completion)
- Query invalidation strategies for maintaining data consistency

### Chat Integration

- Stream Chat client initialized in AppProvider when user is authenticated
- Chat channels and threads managed through global context
- Integration with group/cohort system for contextual messaging

### Navigation Patterns

- Groups → Cohorts → Modules → Live Classes hierarchy
- Courses → Sections → Lessons structure for learning content
- Deep linking support for group and live class URLs

## Important Implementation Details

### Authentication Flow

- SuperTokens session management with automatic session validation
- Stream Chat token generation tied to authentication status
- User ID extraction from SuperTokens session for API calls

### Data Normalization

- Groups data normalized with `byId` and `order` structure for efficient lookups
- Consistent query key patterns for related data hierarchies

### Error Handling

- Try-catch utility in `utils/try-catch.ts` for consistent error handling patterns
- API responses include error messages in standardized format

### File Organization

- Components organized by feature in `components/modules/`
- Reusable UI components in `components/ui/`
- Type definitions split by domain in `types/`
- API types centralized in `lib/api/types.ts`

## Testing

Run tests with Jest using the Expo preset:

```bash
npm run test
```

## Deep Linking Configuration

- iOS: Associated domains configured for `sphere-app.expo.app`
- Android: Intent filters handle group and live class URLs with patterns:
  - `/groups/.*/cohorts/.*/modules/.*/live-classes/.*`
  - `/groups/.*`
- Custom URL scheme: `myapp://`

## Build Configuration

- **New Architecture**: Enabled (`newArchEnabled: true`)
- **iOS**: Static frameworks, minimum deployment target via build properties
- **Android**: Minimum SDK 26, adaptive icon with `#3B4BB4` background
- **Dark Mode**: Primary UI style set to dark with appropriate splash screens
