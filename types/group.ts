export interface CourseModuleConfig {
  courseId: number;
}

export interface Module {
  config: CourseModuleConfig;
  createdAt: string;
  id: number;
  name: string;
  order: number;
  type: string;
}

export interface Group {
  bannerImage: string;
  createdAt: string;
  creatorName: string;
  description: string;
  id: number;
  modules: Module[];
  name: string;
  organizationName: string;
  totalLessons: number;
  totalMembers: number;
}

export type GroupResponse = {
  success: boolean;
  data: Group;
};
