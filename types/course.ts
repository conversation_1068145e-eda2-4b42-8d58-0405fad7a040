export interface LessonAttachment {
  attachmentType: string;
  id: number;
  title: string;
  url: string;
}

export interface LessonContent {
  contentType: string;
  metadata: {
    url: string;
  };
}

export interface Lesson {
  createdAt: string;
  description: string;
  id: number;
  lessonAttachments: LessonAttachment[];
  lessonContent: LessonContent;
  lessonOrder: number;
  title: string;
}

export interface Section {
  createdAt: string;
  id: number;
  lessons: Lesson[];
  name: string;
  sectionOrder: number;
}

export interface Course {
  bannerImage: string;
  createdAt: string;
  description: string;
  id: number;
  name: string;
  sections: Section[];
  tags: string[];
  totalLessons: number;
  totalSections: number;
}

export interface CourseResponse {
  success: boolean;
  data: Course;
}
