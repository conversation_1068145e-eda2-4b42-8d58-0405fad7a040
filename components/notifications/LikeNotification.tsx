import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { BaseCard } from './BaseCard';
import { AvatarGroup } from './AvatarGroup';
import type { LikeNotification } from '@/lib/stream/types';
import { formatTimeAgo } from '@/lib/utils/time';

interface LikeNotificationProps {
  notification: LikeNotification;
  onMarkRead?: (id: string) => void;
}

export function LikeNotification({ notification, onMarkRead }: LikeNotificationProps) {
  const { actors, targetPost } = notification;

  const handlePress = () => {
    if (!notification.isRead && onMarkRead) {
      onMarkRead(notification.id);
    }
  };

  const getActorText = () => {
    if (actors.count === 1) {
      return actors.names[0];
    } else if (actors.count === 2) {
      return `${actors.names[0]} and ${actors.names[1]}`;
    } else {
      return `${actors.names[0]} and ${actors.count - 1} others`;
    }
  };

  return (
    <BaseCard isRead={notification.isRead} onPress={handlePress}>
      <AvatarGroup urls={actors.avatars} />

      <View style={styles.content}>
        <Text style={styles.text}>
          <Text style={styles.actor}>{getActorText()}</Text>
          <Text style={styles.action}> liked your post</Text>
        </Text>

        {targetPost.message && (
          <Text style={styles.message} numberOfLines={2}>
            "{targetPost.message}"
          </Text>
        )}

        <Text style={styles.time}>
          {formatTimeAgo(notification.createdAt)}
        </Text>
      </View>
    </BaseCard>
  );
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    marginLeft: 12,
  },
  text: {
    fontSize: 14,
    marginBottom: 4,
  },
  actor: {
    color: '#fff',
    fontWeight: '600',
  },
  action: {
    color: '#9A9A9A',
  },
  message: {
    fontSize: 14,
    color: '#E0E0E0',
    marginBottom: 8,
  },
  time: {
    fontSize: 12,
    color: '#585858',
  },
});

