import React from "react";
import { View, TouchableOpacity, StyleSheet } from "react-native";

interface BaseCardProps {
  children: React.ReactNode;
  isRead: boolean;
  onPress?: () => void;
}

export function BaseCard({ children, isRead, onPress }: BaseCardProps) {
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        {children}
        {!isRead && <View style={styles.unreadIndicator} />}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#1A1A1A",
    borderRadius: 12,
    marginBottom: 6,
    borderWidth: 1,
    borderColor: "#333",
  },
  content: {
    flexDirection: "row",
    alignItems: "flex-start",
    padding: 16,
  },
  unreadIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#007AFF",
    marginLeft: 8,
    marginTop: 2,
  },
});
