import React from 'react';
import { View, Image, Text, StyleSheet } from 'react-native';

interface ImageGridProps {
  images: string[];
}

export function ImageGrid({ images }: ImageGridProps) {
  if (!images || images.length === 0) return null;

  const displayImages = images.slice(0, 4);
  const extraCount = images.length - 4;

  if (displayImages.length === 1) {
    return (
      <View style={styles.singleImageContainer}>
        <Image source={{ uri: displayImages[0] }} style={styles.singleImage} />
      </View>
    );
  }

  if (displayImages.length === 2) {
    return (
      <View style={styles.doubleImageContainer}>
        {displayImages.map((img, index) => (
          <Image key={index} source={{ uri: img }} style={styles.doubleImage} />
        ))}
      </View>
    );
  }

  return (
    <View style={styles.gridContainer}>
      {displayImages.map((img, index) => (
        <View key={index} style={styles.gridImageWrapper}>
          <Image source={{ uri: img }} style={styles.gridImage} />
          {index === 3 && extraCount > 0 && (
            <View style={styles.extraOverlay}>
              <Text style={styles.extraText}>+{extraCount}</Text>
            </View>
          )}
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  singleImageContainer: {
    width: 64,
    height: 64,
  },
  singleImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  doubleImageContainer: {
    flexDirection: 'row',
    gap: 4,
  },
  doubleImage: {
    width: 32,
    height: 32,
    borderRadius: 4,
  },
  gridContainer: {
    width: 64,
    height: 64,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 2,
  },
  gridImageWrapper: {
    width: 28,
    height: 28,
    position: 'relative',
  },
  gridImage: {
    width: '100%',
    height: '100%',
    borderRadius: 4,
  },
  extraOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  extraText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
});
