import React from "react";
import { View, Text, StyleSheet } from "react-native";
import type { UINotification } from "@/lib/stream/types";
import { PostNotification } from "./PostNotification";
import { LikeNotification } from "./LikeNotification";
import { BaseCard } from "./BaseCard";
import { AvatarGroup } from "./AvatarGroup";
import { formatTimeAgo } from "@/lib/utils/time";

interface NotificationItemProps {
  notification: UINotification;
  onMarkRead?: (id: string) => void;
}

export function NotificationItem({
  notification,
  onMarkRead,
}: NotificationItemProps) {
  switch (notification.verb) {
    case "post":
      return (
        <PostNotification
          notification={notification as any}
          onMarkRead={onMarkRead}
        />
      );

    case "like":
      return (
        <LikeNotification
          notification={notification as any}
          onMarkRead={onMarkRead}
        />
      );

    case "comment":
      return (
        <CommentNotification
          notification={notification}
          onMarkRead={onMarkRead}
        />
      );

    case "follow":
      return (
        <FollowNotification
          notification={notification}
          onMarkRead={onMarkRead}
        />
      );

    case "arrived":
      return (
        <ArrivedNotification
          notification={notification}
          onMarkRead={onMarkRead}
        />
      );

    case "generic":
    default:
      return (
        <GenericNotification
          notification={notification}
          onMarkRead={onMarkRead}
        />
      );
  }
}

// Comment Notification Component
function CommentNotification({
  notification,
  onMarkRead,
}: NotificationItemProps) {
  const { actors } = notification;
  const targetPost = (notification as any).targetPost;
  const commentPreview = (notification as any).commentPreview;

  const handlePress = () => {
    if (!notification.isRead && onMarkRead) {
      onMarkRead(notification.id);
    }
  };

  const getActorText = () => {
    if (actors.count === 1) {
      return actors.names[0];
    } else if (actors.count === 2) {
      return `${actors.names[0]} and ${actors.names[1]}`;
    } else {
      return `${actors.names[0]} and ${actors.count - 1} others`;
    }
  };

  return (
    <BaseCard isRead={notification.isRead} onPress={handlePress}>
      <AvatarGroup urls={actors.avatars} />

      <View style={styles.content}>
        <Text style={styles.text}>
          <Text style={styles.actor}>{getActorText()}</Text>
          <Text style={styles.action}> commented on your post</Text>
        </Text>

        {commentPreview && (
          <View style={styles.commentBox}>
            <Text style={styles.commentText}>"{commentPreview}"</Text>
          </View>
        )}

        {targetPost?.message && (
          <Text style={styles.message} numberOfLines={2}>
            "{targetPost.message}"
          </Text>
        )}

        <Text style={styles.time}>{formatTimeAgo(notification.createdAt)}</Text>
      </View>
    </BaseCard>
  );
}

// Follow Notification Component
function FollowNotification({
  notification,
  onMarkRead,
}: NotificationItemProps) {
  const { actors } = notification;

  const handlePress = () => {
    if (!notification.isRead && onMarkRead) {
      onMarkRead(notification.id);
    }
  };

  const getActorText = () => {
    if (actors.count === 1) {
      return actors.names[0];
    } else if (actors.count === 2) {
      return `${actors.names[0]} and ${actors.names[1]}`;
    } else {
      return `${actors.names[0]} and ${actors.count - 1} others`;
    }
  };

  return (
    <BaseCard isRead={notification.isRead} onPress={handlePress}>
      <AvatarGroup urls={actors.avatars} />

      <View style={styles.content}>
        <Text style={styles.text}>
          <Text style={styles.actor}>{getActorText()}</Text>
          <Text style={styles.action}> started following you</Text>
        </Text>

        <Text style={styles.time}>{formatTimeAgo(notification.createdAt)}</Text>
      </View>
    </BaseCard>
  );
}

// Arrived Notification Component
function ArrivedNotification({
  notification,
  onMarkRead,
}: NotificationItemProps) {
  const { actors, message } = notification;

  const handlePress = () => {
    if (!notification.isRead && onMarkRead) {
      onMarkRead(notification.id);
    }
  };

  const getActorText = () => {
    if (actors.count === 1) {
      return actors.names[0];
    } else if (actors.count === 2) {
      return `${actors.names[0]} and ${actors.names[1]}`;
    } else {
      return `${actors.names[0]} and ${actors.count - 1} others`;
    }
  };

  return (
    <BaseCard isRead={notification.isRead} onPress={handlePress}>
      <AvatarGroup urls={actors.avatars} />

      <View style={styles.content}>
        <Text style={styles.text}>
          <Text style={styles.actor}>{getActorText()}</Text>
          <Text style={styles.action}> joined your cohort</Text>
        </Text>

        {message && message !== "Someone arrived" && (
          <Text style={styles.message}>"{message}"</Text>
        )}

        <Text style={styles.time}>{formatTimeAgo(notification.createdAt)}</Text>
      </View>
    </BaseCard>
  );
}

// Generic Notification Component
function GenericNotification({
  notification,
  onMarkRead,
}: NotificationItemProps) {
  const { actors, message } = notification;

  const handlePress = () => {
    if (!notification.isRead && onMarkRead) {
      onMarkRead(notification.id);
    }
  };

  const getActorText = () => {
    if (actors.count === 1) {
      return actors.names[0];
    } else if (actors.count === 2) {
      return `${actors.names[0]} and ${actors.names[1]}`;
    } else {
      return `${actors.names[0]} and ${actors.count - 1} others`;
    }
  };

  return (
    <BaseCard isRead={notification.isRead} onPress={handlePress}>
      <AvatarGroup urls={actors.avatars} />

      <View style={styles.content}>
        <Text style={styles.text}>
          <Text style={styles.actor}>{getActorText()}</Text>
          <Text style={styles.action}> had new activity</Text>
        </Text>

        {message && <Text style={styles.message}>"{message}"</Text>}

        <Text style={styles.time}>{formatTimeAgo(notification.createdAt)}</Text>
      </View>
    </BaseCard>
  );
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    marginLeft: 12,
  },
  text: {
    fontSize: 14,
    marginBottom: 4,
  },
  actor: {
    color: "#fff",
    fontWeight: "600",
  },
  action: {
    color: "#9A9A9A",
  },
  message: {
    fontSize: 14,
    color: "#E0E0E0",
    marginBottom: 8,
  },
  time: {
    fontSize: 12,
    color: "#585858",
  },
  commentBox: {
    backgroundColor: "#333",
    borderRadius: 8,
    padding: 8,
    marginVertical: 8,
  },
  commentText: {
    fontSize: 14,
    color: "#E0E0E0",
    fontStyle: "italic",
  },
});
