import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { BaseCard } from "./BaseCard";
import { AvatarGroup } from "./AvatarGroup";
import { ImageGrid } from "./ImageGrid";
import type { PostNotification } from "@/lib/stream/types";
import { formatTimeAgo } from "@/lib/utils/time";

interface PostNotificationProps {
  notification: PostNotification;
  onMarkRead?: (id: string) => void;
}

export function PostNotification({
  notification,
  onMarkRead,
}: PostNotificationProps) {
  const { actors, post, activityCount } = notification;

  const handlePress = () => {
    if (!notification.isRead && onMarkRead) {
      onMarkRead(notification.id);
    }
  };

  const getActorText = () => {
    if (actors.count === 1) {
      return actors.names[0];
    } else if (actors.count === 2) {
      return `${actors.names[0]} and ${actors.names[1]}`;
    } else {
      return `${actors.names[0]} and ${actors.count - 1} others`;
    }
  };

  const getPostText = () => {
    if (activityCount === 1) {
      return "posted";
    } else {
      return `made ${activityCount} posts`;
    }
  };

  return (
    <BaseCard isRead={notification.isRead} onPress={handlePress}>
      <AvatarGroup urls={actors.avatars} />

      <View style={styles.content}>
        <Text style={styles.text}>
          <Text style={styles.actor}>{getActorText()}</Text>
          <Text style={styles.action}> {getPostText()}</Text>
        </Text>

        {post.message && (
          <Text style={styles.message} numberOfLines={2}>
            "{post.message}"
          </Text>
        )}

        <View style={styles.footer}>
          <Text style={styles.time}>
            {formatTimeAgo(notification.createdAt)}
          </Text>
        </View>
      </View>

      {post.images && post.images.length > 0 && (
        <View style={styles.imageContainer}>
          <ImageGrid images={post.images} />
        </View>
      )}
    </BaseCard>
  );
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    marginLeft: 12,
  },
  text: {
    fontSize: 14,
    marginBottom: 4,
  },
  actor: {
    color: "#fff",
    fontWeight: "600",
  },
  action: {
    color: "#9A9A9A",
  },
  message: {
    fontSize: 14,
    color: "#E0E0E0",
    marginBottom: 8,
  },
  imageContainer: {
    marginLeft: 12,
    alignSelf: "flex-start",
  },
  imagesContainer: {
    marginBottom: 8,
  },
  time: {
    fontSize: 12,
    color: "#585858",
  },
  footer: {
    marginTop: 8,
  },
});
