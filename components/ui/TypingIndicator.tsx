import React, { useEffect, useRef } from "react";
import { View, Text, StyleSheet, Animated, Easing } from "react-native";
import {
  useTypingContext,
  useChatContext,
  useChannelContext,
  useThreadContext,
} from "stream-chat-expo";

interface CustomTypingIndicatorProps {
  threadList?: boolean;
}

export const CustomTypingIndicator: React.FC<CustomTypingIndicatorProps> = ({
  threadList = false,
}) => {
  const { typing = {} } = useTypingContext();
  const { client } = useChatContext();
  const { channel } = useChannelContext();
  const { thread } = useThreadContext();

  // Animation values for the dots
  const dot1Anim = useRef(new Animated.Value(0.3)).current;
  const dot2Anim = useRef(new Animated.Value(0.3)).current;
  const dot3Anim = useRef(new Animated.Value(0.3)).current;

  // Check if typing events are enabled
  if (channel?.data?.config?.typing_events === false) {
    return null;
  }

  // Filter typing users based on context (channel vs thread)
  const typingUsers = Object.values(typing).filter(
    ({ parent_id, user }: any) => {
      if (user?.id === client?.user?.id) return false; // Exclude current user

      if (threadList) {
        return parent_id === thread?.id; // Only show typing in current thread
      } else {
        return !parent_id; // Only show typing in main channel (not in threads)
      }
    }
  );

  const isTyping = typingUsers.length > 0;

  // Animate dots when typing
  useEffect(() => {
    if (isTyping) {
      // Create staggered dot animation
      const createDotAnimation = (animValue: Animated.Value, delay: number) => {
        return Animated.loop(
          Animated.sequence([
            Animated.timing(animValue, {
              toValue: 1,
              duration: 600,
              delay,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: true,
            }),
            Animated.timing(animValue, {
              toValue: 0.3,
              duration: 600,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: true,
            }),
          ])
        );
      };

      const dot1Animation = createDotAnimation(dot1Anim, 0);
      const dot2Animation = createDotAnimation(dot2Anim, 200);
      const dot3Animation = createDotAnimation(dot3Anim, 400);

      dot1Animation.start();
      dot2Animation.start();
      dot3Animation.start();

      return () => {
        dot1Animation.stop();
        dot2Animation.stop();
        dot3Animation.stop();
      };
    }
  }, [isTyping, dot1Anim, dot2Anim, dot3Anim]);

  if (!isTyping) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.dotsContainer}>
        <Animated.View
          style={[
            styles.dot,
            {
              opacity: dot1Anim,
              transform: [
                {
                  scale: dot1Anim.interpolate({
                    inputRange: [0.3, 1],
                    outputRange: [0.8, 1.2],
                  }),
                },
              ],
            },
          ]}
        />
        <Animated.View
          style={[
            styles.dot,
            {
              opacity: dot2Anim,
              transform: [
                {
                  scale: dot2Anim.interpolate({
                    inputRange: [0.3, 1],
                    outputRange: [0.8, 1.2],
                  }),
                },
              ],
            },
          ]}
        />
        <Animated.View
          style={[
            styles.dot,
            {
              opacity: dot3Anim,
              transform: [
                {
                  scale: dot3Anim.interpolate({
                    inputRange: [0.3, 1],
                    outputRange: [0.8, 1.2],
                  }),
                },
              ],
            },
          ]}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: "flex-start",
    backgroundColor: "black",
  },
  dotsContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#131313",
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 20,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: "#888",
    marginHorizontal: 2,
  },
});
