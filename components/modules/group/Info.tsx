import {
  View,
  Text,
  StyleSheet,
  useWindowDimensions,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
} from "react-native";
import { WebView } from "react-native-webview";
import { useState, useCallback } from "react";
import { MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";

import { useGroup } from "@/lib/api/queries";

interface Props {
  groupId: string;
  bio: string | null;
}

export const Info = ({ groupId, bio }: Props) => {
  const { data: groupData, refetch } = useGroup(groupId);
  const [webViewHeight, setWebViewHeight] = useState(0);
  const [refreshing, setRefreshing] = useState(false);
  const { width } = useWindowDimensions();

  const onRefresh = useCallback(async () => {
    console.log("Info: Pull to refresh started, groupId:", groupId);
    setRefreshing(true);
    try {
      console.log("Info: Calling refetch for group data...");
      await refetch();
      console.log("Info: Refetch completed successfully");
    } catch (error) {
      console.error("Info: Refetch failed:", error);
    } finally {
      setRefreshing(false);
      console.log("Info: Pull to refresh finished");
    }
  }, [refetch, groupId]);

  const defaultContent = "<h1>Welcome to the Cohort!</h1>";

  const injectedJavaScript = `
    window.ReactNativeWebView.postMessage(
      Math.max(
        document.documentElement.scrollHeight,
        document.documentElement.offsetHeight
      )
    );
    true;
  `;

  const htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
        <style>
          html, body {
            margin: 0;
            padding: 0 8px;
            background-color: #000;
            font-family: -apple-system, system-ui;
            width: ${width}px;
            overflow: hidden;
          }
          * {
            box-sizing: border-box;
            color: #FFFFFF;
          }
          h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 24px;
            color: #FFFFFF;
          }
          p {
            font-size: 16px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
          }
          ul, ol {
            list-style-type: none;
            padding: 0;
            margin: 20px 0;
          }
          li {
            font-size: 16px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 12px;
            padding-left: 24px;
            position: relative;
          }
          li:before {
            content: "•";
            position: absolute;
            left: 0;
            color: rgba(255, 255, 255, 0.8);
          }
          h2 {
            font-size: 20px;
            font-weight: 600;
            margin-top: 32px;
            margin-bottom: 20px;
            color: #FFFFFF;
          }
          img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 16px 0;
          }
        </style>
      </head>
      <body>
        <div>
          ${bio || defaultContent}
        </div>
      </body>
    </html>
  `;

  const onMessage = (event: any) => {
    const height = parseInt(event.nativeEvent.data);
    setWebViewHeight(height);
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor="#fff"
            colors={["#fff"]}
          />
        }
      >
        <View style={styles.contentContainer}>
          <View style={[styles.webviewWrapper, { height: webViewHeight }]}>
            <WebView
              style={styles.webview}
              scrollEnabled={false}
              originWhitelist={["*"]}
              source={{ html: htmlContent }}
              injectedJavaScript={injectedJavaScript}
              onMessage={onMessage}
              onError={(syntheticEvent) => {
                console.warn("WebView error:", syntheticEvent.nativeEvent);
              }}
              showsVerticalScrollIndicator={false}
              domStorageEnabled={true}
              javaScriptEnabled={true}
            />
          </View>
          <View style={styles.aboutCreatorContainerWrapper}>
            <TouchableOpacity
              onPress={() => {
                if (groupData?.data?.creatorSupertokensUserId) {
                  router.push({
                    pathname: "/profile/[id]",
                    params: { id: groupData.data.creatorSupertokensUserId },
                  });
                }
              }}
            >
              <View style={styles.aboutCreatorContainer}>
                <Text style={styles.sectionTitle}>About Creator</Text>
                <View style={styles.creatorInfo}>
                  <View style={styles.creatorProfile}>
                    <MaterialIcons name="person" size={20} color="#fff" />
                    <Text style={styles.creatorName}>
                      {groupData?.data?.creatorName}
                    </Text>
                  </View>
                  <Text style={styles.creatorBio}>
                    {groupData?.data?.creatorBio}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // flex: 1,
    height: "100%",
  },
  contentContainer: {
    padding: 0,
  },
  scrollView: {
    flex: 1,
  },
  webviewWrapper: {
    height: "100%",
  },
  webview: {
    flex: 1,
    backgroundColor: "#000",
    opacity: 0.99, // hack to force the webview to render on android
  },
  aboutCreatorContainer: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: "#1E1E1E",
    gap: 16,
    marginTop: 12,
    marginBottom: 20,
  },
  aboutCreatorContainerWrapper: {
    padding: 16,
  },
  sectionTitle: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  creatorInfo: {
    gap: 16,
  },
  creatorProfile: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  creatorName: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
  },
  creatorBio: {
    color: "#838A94",
    fontSize: 14,
    lineHeight: 20,
  },
});
