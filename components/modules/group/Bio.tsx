import { View, StyleSheet, useWindowDimensions } from "react-native";
import { WebView } from "react-native-webview";
import { useState } from "react";

interface Props {
  bio: string;
}

export const Bio = ({ bio }: Props) => {
  const [webViewHeight, setWebViewHeight] = useState(0);
  const { width } = useWindowDimensions();

  const injectedJavaScript = `
    (function() {
      const content = document.querySelector('.bio-container');
      const height = content.scrollHeight;
      window.ReactNativeWebView.postMessage(height.toString());
    })();
    true;
  `;

  const htmlContent = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
                <style>
          html, body {
            margin: 0;
            padding: 0;
            background-color: #000;
            font-family: -apple-system, system-ui;
            width: ${width}px;
            overflow: hidden;
          }
          .bio-container {
            padding: 0 16px;
            display: flex;
            flex-direction: column;
          }
          * {
            box-sizing: border-box;
            color: #FFFFFF;
          }

          /* Prose Headings */
          h1, h2, h3, h4, h5, h6, .prose-heading {
            font-weight: 600;
            color: #FFFFFF;
          }

          h1, .prose-heading[data-level="1"] {
            font-size: 2em;
            font-weight: 700;
          }

          h2, .prose-heading[data-level="2"] {
            font-size: 1.5em;
          }

          h3, .prose-heading[data-level="3"] {
            font-size: 1.25em;
          }

          /* Paragraphs */
          p {
            font-size: 16px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.8);
            margin: 0.5em 0;
          }

          p:first-child {
            margin-top: 0;
          }

          p:last-child {
            margin-bottom: 0;
          }

          /* Blockquotes */
          blockquote, .prose-blockquote {
            position: relative;
            border-left: 4px solid rgba(229, 231, 235, 0.3);
            margin: 0.5em 0;
            padding: 1em 1.5em;
            color: rgba(255, 255, 255, 0.6);
            font-style: italic;
            background: rgba(249, 250, 251, 0.05);
            border-radius: 0;
            box-shadow: none;
          }

          blockquote p, .prose-blockquote p {
            margin: 0;
            color: rgba(255, 255, 255, 0.6);
          }

          blockquote:hover, .prose-blockquote:hover {
            background: rgba(243, 244, 246, 0.08);
            transition: background-color 0.2s ease;
          }

          /* Lists */
          ul, ol, .prose-bullet-list, .prose-ordered-list {
            margin: 0.5em 0;
            padding-left: 1.5em;
          }

          li, .prose-list-item {
            font-size: 16px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.8);
            margin: 0.5em 0;
            position: relative;
          }

          ul li, .prose-bullet-list .prose-list-item {
            list-style-type: disc;
            list-style-position: outside;
          }

          ol li, .prose-ordered-list .prose-list-item {
            list-style-type: decimal;
            list-style-position: outside;
          }

          /* Links */
          a, .prose-link {
            color: #60a5fa;
            text-decoration: underline;
            text-underline-offset: 2px;
            position: relative;
          }

          a:hover, .prose-link:hover {
            text-decoration: none;
            background: rgba(96, 165, 250, 0.1);
            border-radius: 0.25rem;
            padding: 0 0.125rem;
          }

          /* Horizontal rules */
          hr, .prose-hr {
            border: none;
            border-top: 2px solid rgba(255, 255, 255, 0.2);
            margin: 0.75em 0;
            position: relative;
          }

          hr::before, .prose-hr::before {
            content: '• • •';
            position: absolute;
            left: 50%;
            top: -0.5em;
            transform: translateX(-50%);
            background: #000;
            padding: 0 1em;
            color: rgba(255, 255, 255, 0.4);
            font-size: 0.875em;
          }

          /* Strikethrough */
          s, del, .prose-strikethrough {
            text-decoration: line-through;
            text-decoration-color: currentColor;
            text-decoration-thickness: 2px;
          }

          /* Text alignment */
          [style*="text-align: center"] {
            text-align: center;
          }

          [style*="text-align: right"] {
            text-align: right;
          }

          [style*="text-align: left"] {
            text-align: left;
          }

          /* Images */
          img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 16px 0;
          }

          /* Quote styling */
          q {
            font-style: italic;
            color: rgba(255, 255, 255, 0.9);
            quotes: """ """ "'" "'";
          }

          q:before {
            content: open-quote;
            color: rgba(255, 255, 255, 0.6);
          }

          q:after {
            content: close-quote;
            color: rgba(255, 255, 255, 0.6);
          }
        </style>
      </head>
      <body>
        <div class="bio-container">
          ${bio}
        </div>
      </body>
    </html>
  `;

  const onMessage = (event: any) => {
    const height = parseInt(event.nativeEvent.data);
    setWebViewHeight(height);
  };

  return (
    <View style={styles.container}>
      <View style={[styles.webviewWrapper, { height: webViewHeight }]}>
        <WebView
          style={styles.webview}
          scrollEnabled={false}
          originWhitelist={["*"]}
          source={{ html: htmlContent }}
          injectedJavaScript={injectedJavaScript}
          onMessage={onMessage}
          onError={(syntheticEvent) => {
            console.warn("WebView error:", syntheticEvent.nativeEvent);
          }}
          showsVerticalScrollIndicator={false}
          domStorageEnabled={true}
          javaScriptEnabled={true}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  webviewWrapper: {},
  webview: {
    flex: 1,
    backgroundColor: "#000",
    opacity: 0.99,
  },
});
