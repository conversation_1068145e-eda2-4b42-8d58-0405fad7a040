import {
  View,
  Text,
  StyleSheet,
  Pressable,
  Linking,
  ScrollView,
  RefreshControl,
} from "react-native";
import { Link2, File } from "lucide-react-native";
import { useState, useCallback } from "react";

import type { LinksModule } from "@/lib/api/types";
import { useMyCohortModule } from "@/lib/api/queries";

interface Props {
  module: LinksModule;
  groupId: string;
  cohortId: string;
}

type FilterType = "All" | "Links" | "Attachments";

export const Links = ({ module, groupId, cohortId }: Props) => {
  const [activeFilter, setActiveFilter] = useState<FilterType>("All");
  const [refreshing, setRefreshing] = useState(false);
  const { data: moduleData, refetch: refetchCohortModule } = useMyCohortModule(
    groupId,
    cohortId,
    module.id.toString()
  );

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetchCohortModule();
    } catch (error) {
      console.error("Links: Refresh failed:", error);
    } finally {
      setRefreshing(false);
    }
  }, [refetchCohortModule]);

  const handleOpenLink = async (url: string) => {
    try {
      await Linking.openURL(url);
    } catch (error) {
      console.error("Error opening URL:", error);
    }
  };

  const renderIcon = (type: string) => {
    if (type === "link") {
      return <Link2 size={20} color="#ffffff" />;
    }
    return <File size={20} color="#ffffff" />;
  };

  const getIconContainerStyle = (type: string) => {
    return [
      styles.iconContainer,
      type === "link" ? styles.linkIconContainer : styles.documentIconContainer,
    ];
  };

  const filteredAttachments = (
    moduleData?.data as LinksModule
  )?.config.attachments.filter((attachment) => {
    if (activeFilter === "All") return true;
    if (activeFilter === "Links") return attachment.attachmentType === "link";
    return attachment.attachmentType === "document";
  });

  return (
    <View style={styles.container}>
      <View style={styles.filterContainer}>
        {["All", "Links", "Attachments"].map((filter) => (
          <Pressable
            key={filter}
            style={[
              styles.filterButton,
              activeFilter === filter && styles.activeFilterButton,
            ]}
            onPress={() => setActiveFilter(filter as FilterType)}
          >
            <Text
              style={[
                styles.filterText,
                activeFilter === filter && styles.activeFilterText,
              ]}
            >
              {filter}
            </Text>
          </Pressable>
        ))}
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={{ flexGrow: 1 }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor="#fff"
            colors={["#fff"]}
          />
        }
      >
        <View style={styles.content}>
          {filteredAttachments?.map((attachment, index) => (
            <Pressable
              key={`${attachment.title}-${index}`}
              style={styles.linkItem}
              onPress={() => handleOpenLink(attachment.url)}
            >
              <View style={getIconContainerStyle(attachment.attachmentType)}>
                {renderIcon(attachment.attachmentType)}
              </View>
              <View style={styles.contentContainer}>
                <Text style={styles.title} numberOfLines={1}>
                  {attachment.title}
                </Text>
                <Text style={styles.url} numberOfLines={1}>
                  {attachment.url}
                </Text>
              </View>
            </Pressable>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // flex: 1,
    height: "100%",
  },
  scrollView: {
    // flex: 1,
  },
  filterContainer: {
    flexDirection: "row",
    paddingTop: 12,
    paddingBottom: 24,
    gap: 8,
  },
  filterButton: {
    paddingVertical: 6,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: "#2C2E33",
  },
  activeFilterButton: {
    backgroundColor: "#FFFFFF",
  },
  filterText: {
    color: "#6B7280",
    fontSize: 14,
    fontWeight: "500",
  },
  activeFilterText: {
    color: "#000000",
  },
  content: {
    flex: 1,
  },
  monthHeader: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#FFFFFF",
    paddingVertical: 8,
    marginBottom: 4,
  },
  linkItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomColor: "#2C2E33",
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  linkIconContainer: {
    backgroundColor: "#637381",
  },
  documentIconContainer: {
    backgroundColor: "#2C2E33",
  },
  contentContainer: {
    flex: 1,
    marginRight: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: "500",
    color: "#FFFFFF",
    marginBottom: 4,
  },
  url: {
    fontSize: 12,
    color: "#6B7280",
    opacity: 0.8,
  },
  date: {
    fontSize: 12,
    color: "#6B7280",
    opacity: 0.8,
  },
});
