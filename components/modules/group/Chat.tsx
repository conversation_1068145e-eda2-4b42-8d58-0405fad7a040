import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import type { DiscussionModule } from "@/lib/api/types";
import { MessageCircle } from "lucide-react-native";
import { router } from "expo-router";

interface Props {
  discussionModules: DiscussionModule[];
}

export const Chat = ({ discussionModules }: Props) => {
  console.log("Chat modules:", discussionModules);

  return (
    <ScrollView>
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          <View style={styles.scrollView}>
            {discussionModules
              .filter((module) => Object.keys(module.config).length > 0)
              .map((module) => {
                return (
                  <TouchableOpacity
                    key={module.id}
                    style={styles.discussionModuleContainer}
                    onPress={() =>
                      router.push(
                        `/(app)/chat/channel/${module.config?.channelId}`
                      )
                    }
                  >
                    <MessageCircle size={24} color="white" />
                    <View style={styles.discussionModuleTitleContainer}>
                      <Text style={styles.discussionModuleTitle}>
                        {module.name}
                      </Text>
                      <Text style={styles.cta}>Tap to chat</Text>
                    </View>
                  </TouchableOpacity>
                );
              })}
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    height: "100%",
    padding: 16,
  },
  contentContainer: {
    padding: 0,
  },
  scrollView: {
    flex: 1,
  },
  discussionModuleContainer: {
    padding: 16,
    borderWidth: 1,
    borderColor: "#4a4a4a",
    borderRadius: 10,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  discussionModuleTitleContainer: {
    borderColor: "#4a4a4a",
    textAlign: "right",
  },
  discussionModuleTitle: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "600",
    textAlign: "right",
  },
  cta: {
    color: "#6a6a6a",
    fontSize: 12,
    fontWeight: "400",
  },
});
