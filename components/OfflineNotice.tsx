import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet } from "react-native";
import NetInfo from "@react-native-community/netinfo";

export default function OfflineNotice() {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      setIsConnected(state.isConnected);
    });
    return () => unsubscribe();
  }, []);

  if (isConnected === false) {
    return (
      <View style={styles.banner}>
        <Text style={styles.text}>You are offline</Text>
      </View>
    );
  }
  return null;
}

const styles = StyleSheet.create({
  banner: {
    backgroundColor: "#b52424",
    height: 45,
    paddingTop: 8,
    alignItems: "center",
  },
  text: {
    color: "#fff",
    fontSize: 13,
  },
});
