import {
  createContext,
  useContext,
  useEffect,
  useState,
  type ReactNode,
} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import SuperTokens from "supertokens-react-native";
import {
  Chat,
  OverlayProvider,
  SqliteClient,
  Streami18n,
  useCreateChatClient,
} from "stream-chat-expo";
import { connect } from "getstream";

import { useGenerateGetStreamToken } from "@/lib/api/queries";

const GROUP_KEY = "current_group_id";
const COHORT_KEY = "current_cohort_id";
const STREAM_API_KEY = process.env.EXPO_PUBLIC_STREAM_API_KEY!;
const STREAM_APP_ID = process.env.EXPO_PUBLIC_STREAM_APP_ID!;

type AppContextType = {
  // isAuthenticated: boolean;
  chatClient: ReturnType<typeof useCreateChatClient>;
  streamClient: any;
  userId: string | null;
  groupId: string | null;
  cohortId: string | null;
  streamToken: string | null;
  thread: any;
  channel: any;
  setIsAuthenticated: (value: boolean) => void;
  setUserId: (id: string | null) => void;
  setGroupId: (id: string | null) => void;
  setCohortId: (id: string | null) => void;
  setThread: (thread: any) => void;
  setChannel: (channel: any) => void;
  cleanup: () => void;
};

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);
  const [groupId, setGroupId] = useState<string | null>(null);
  const [cohortId, setCohortId] = useState<string | null>(null);
  const [thread, setThread] = useState<any>(null);
  const [channel, setChannel] = useState<any>(null);
  const [streamClient, setStreamClient] = useState<any>(null);

  const { mutate: generateGetStreamToken, data: getStreamToken } =
    useGenerateGetStreamToken();

  const chatClient = useCreateChatClient({
    apiKey: isAuthenticated ? STREAM_API_KEY : "",
    userData: { id: userId! },
    tokenOrProvider: getStreamToken?.data?.token,
  });

  useEffect(() => {
    SuperTokens.doesSessionExist().then((exist) => {
      setIsAuthenticated(exist);
    });
  }, []);

  useEffect(() => {
    if (isAuthenticated) {
      SuperTokens.getUserId()
        .then((userId) => {
          console.log("userId", userId);
          setUserId(userId);
        })
        .catch((error) => {
          console.error("Failed to get user ID:", error);
        });
    }
  }, [isAuthenticated]);

  useEffect(() => {
    if (isAuthenticated) {
      generateGetStreamToken();
    }
  }, [isAuthenticated]);

  // Initialize GetStream client when we have token
  useEffect(() => {
    if (getStreamToken?.data?.token && STREAM_API_KEY && STREAM_APP_ID) {
      try {
        const client = connect(
          STREAM_API_KEY,
          getStreamToken.data.token,
          STREAM_APP_ID
        );
        setStreamClient(client);
      } catch (error) {
        console.error("Failed to initialize GetStream client:", error);
      }
    }
  }, [getStreamToken?.data?.token]);

  useEffect(() => {
    console.log("AppProvider useEffect");
    if (isAuthenticated) {
      const loadStoredIds = async () => {
        try {
          const [storedGroupId, storedCohortId] = await Promise.all([
            AsyncStorage.getItem(GROUP_KEY),
            AsyncStorage.getItem(COHORT_KEY),
          ]);
          if (storedGroupId) setGroupId(storedGroupId);
          if (storedCohortId) setCohortId(storedCohortId);
        } catch (error) {
          console.error("Failed to load stored IDs:", error);
        }
      };

      loadStoredIds();
    }
  }, [isAuthenticated]);

  const setGroupIdWithStorage = async (id: string | null) => {
    try {
      if (id) {
        await AsyncStorage.setItem(GROUP_KEY, id);
      } else {
        await AsyncStorage.removeItem(GROUP_KEY);
      }
      setGroupId(id);
    } catch (error) {
      console.error("Failed to save group ID:", error);
    }
  };

  const setCohortIdWithStorage = async (id: string | null) => {
    try {
      if (id) {
        await AsyncStorage.setItem(COHORT_KEY, id);
      } else {
        await AsyncStorage.removeItem(COHORT_KEY);
      }
      setCohortId(id);
    } catch (error) {
      console.error("Failed to save cohort ID:", error);
    }
  };

  const cleanup = async () => {
    try {
      await Promise.all([
        AsyncStorage.removeItem(GROUP_KEY),
        AsyncStorage.removeItem(COHORT_KEY),
      ]);

      setGroupId(null);
      setCohortId(null);
      setUserId(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error("Failed to cleanup AppContext:", error);
    }
  };

  return (
    <AppContext.Provider
      value={{
        chatClient,
        streamClient,
        userId,
        groupId,
        cohortId,
        thread,
        channel,
        streamToken: getStreamToken?.data?.token,
        setIsAuthenticated,
        setUserId,
        setGroupId: setGroupIdWithStorage,
        setCohortId: setCohortIdWithStorage,
        setThread,
        setChannel,
        cleanup,
      }}
    >
      {children}
    </AppContext.Provider>
  );
}

export function useAppContext() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error("useAppContext must be used within an AppProvider");
  }
  return context;
}
