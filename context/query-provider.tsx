import React from "react";
import {
  QueryClient,
  QueryClientProvider,
  Query<PERSON>ache,
  MutationCache,
} from "@tanstack/react-query";
import { router } from "expo-router";
import SuperTokens from "supertokens-react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useAppContext } from "@/context/app";

import { HttpError } from "@/lib/api/types";

const STALE_TIME = 10 * 1000; // 10 seconds
const GC_TIME = 5 * 60 * 1000; // 5 minutes

// Check if error is an HttpError with status code
const isHttpError = (error: unknown): error is HttpError => {
  return error instanceof HttpError && typeof error.status === "number";
};

export function QueryProvider({ children }: { children: React.ReactNode }) {
  const { cleanup } = useAppContext();

  const queryClient = React.useMemo(() => {
    const qc = new QueryClient({
      defaultOptions: {
        queries: {
          staleTime: STALE_TIME,
          gcTime: GC_TIME,
          retry: 3,
        },
      },
      queryCache: new QueryCache({
        onError: async (error: unknown) => {
          if (
            isHttpError(error) &&
            (error.status === 401 || error.status === 403)
          ) {
            await SuperTokens.signOut();
            cleanup();
            await AsyncStorage.removeItem("current_group_id");
            await AsyncStorage.removeItem("current_cohort_id");
            qc.clear();
            router.replace("/sign-in");
          }
        },
      }),
      mutationCache: new MutationCache({
        onError: async (error: unknown) => {
          if (
            isHttpError(error) &&
            (error.status === 401 || error.status === 403)
          ) {
            await SuperTokens.signOut();
            cleanup();
            await AsyncStorage.removeItem("current_group_id");
            await AsyncStorage.removeItem("current_cohort_id");
            qc.clear();
            router.replace("/sign-in");
          }
        },
      }),
    });
    return qc;
  }, [cleanup]);

  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}
